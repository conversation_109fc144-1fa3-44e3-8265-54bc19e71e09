---
navigation:
  parent: ae2-mechanics/ae2-mechanics-index.md
  title: Devices
  icon: interface
---

# Devices

A "Device" refers to an AE2 network component that performs some function related to the network itself. They almost always
require a channel, with the notable exception of [Level Emitters](../items-blocks-machines/level_emitter.md).

Some examples include:

*   <ItemLink id="interface" />
*   <ItemLink id="import_bus" />
*   <ItemLink id="storage_bus" />
*   <ItemLink id="pattern_provider" />
*   <ItemLink id="drive" />
