---
navigation:
  parent: items-blocks-machines/items-blocks-machines-index.md
  title: Decorative Sky Stone
  icon: sky_stone_stairs
  position: 010
categories:
- misc ingredients blocks
item_ids:
- ae2:sky_stone_brick
- ae2:sky_stone_small_brick
- ae2:sky_stone_stairs
- ae2:smooth_sky_stone_stairs
- ae2:sky_stone_brick_stairs
- ae2:sky_stone_small_brick_stairs
- ae2:sky_stone_wall
- ae2:smooth_sky_stone_wall
- ae2:sky_stone_brick_wall
- ae2:sky_stone_small_brick_wall
- ae2:sky_stone_slab
- ae2:smooth_sky_stone_slab
- ae2:sky_stone_brick_slab
- ae2:sky_stone_small_brick_slab
---

# Decorative Sky Stone

<Row>
  <GameScene zoom="3" background="transparent">
    <ImportStructure src="../assets/assemblies/decorative_sky_stone.snbt" />
    <IsometricCamera yaw="195" pitch="30" />
  </GameScene>
  <BlockImage id="sky_stone_chest" scale="4" />
  <BlockImage id="smooth_sky_stone_chest" scale="4" />
</Row>

<ItemLink id="sky_stone_block" /> blocks can be crafted and stonecut into some decorative building blocks

## Recipes

<Column gap="5">
  <Row>
    <RecipeFor id="sky_stone_chest" />

    <RecipeFor id="smooth_sky_stone_chest" />
  </Row>

  <Row gap="23">
    <RecipeFor id="sky_stone_brick" />

    <RecipeFor id="sky_stone_small_brick" />
  </Row>

  <Row>
    <RecipeFor id="sky_stone_stairs" />

    <RecipeFor id="smooth_sky_stone_stairs" />
  </Row>

  <Row>
    <RecipeFor id="sky_stone_brick_stairs" />

    <RecipeFor id="sky_stone_small_brick_stairs" />
  </Row>

  <Row>
    <RecipeFor id="sky_stone_wall" />

    <RecipeFor id="smooth_sky_stone_wall" />
  </Row>

  <Row>
    <RecipeFor id="sky_stone_brick_wall" />

    <RecipeFor id="sky_stone_small_brick_wall" />
  </Row>

  <Row>
    <RecipeFor id="sky_stone_slab" />

    <RecipeFor id="smooth_sky_stone_slab" />
  </Row>

  <Row>
    <RecipeFor id="sky_stone_brick_slab" />

    <RecipeFor id="sky_stone_small_brick_slab" />
  </Row>
</Column>
