---
navigation:
  parent: items-blocks-machines/items-blocks-machines-index.md
  title: Wireless Terminals
  icon: wireless_crafting_terminal
  position: 410
categories:
- tools
item_ids:
- ae2:wireless_terminal
- ae2:wireless_crafting_terminal
---

# Wireless Terminals

<Row>
  <ItemImage id="wireless_terminal" scale="4" />

  <ItemImage id="wireless_crafting_terminal" scale="4" />
</Row>

Wireless terminals are portable versions of the regular wired [terminals](terminals.md). They have the exact same UIs as their
wired counterparts, except instead of slots for <ItemLink id="view_cell" />s, they have slots for [upgrade cards](upgrade_cards.md)

In order to pair them with a network, insert the terminal in the top-right slot of a <ItemLink id="wireless_access_point" />
connected to that network. (The slot with a picture of a wireless terminal on it and an arrow below it)

They must be within range of a <ItemLink id="wireless_access_point" /> to function.

Their energy can be recharged in a <ItemLink id="charger" />.

# Wireless Terminal

<ItemImage id="wireless_terminal" scale="4" />

Your basic terminal, now portable! View and access the contents of your [network's storage](../ae2-mechanics/import-export-storage.md)
and request things from your [autocrafting](../ae2-mechanics/autocrafting.md) setup from anywhere within the range of a
<ItemLink id="wireless_access_point" />.

## The UI

See [terminals](terminals.md)

## Upgrades

The Wireless Terminal supports the following [upgrades](upgrade_cards.md):

*   <ItemLink id="energy_card" /> in order to increase the battery capacity

## Recipe

<RecipeFor id="wireless_terminal" />

# Wireless Crafting Terminal

<ItemImage id="wireless_crafting_terminal" scale="4" />

The Wireless Crafting Terminal is similar to a regular wireless terminal, with all the same settings and sections, but with an added crafting grid that will be automatically
refilled from [network storage](../ae2-mechanics/import-export-storage.md). Be careful when shift-clicking the output!

## The UI

See [terminals](terminals.md)

## Upgrades

The Wireless Crafting Terminal supports the following [upgrades](upgrade_cards.md):

*   <ItemLink id="energy_card" /> in order to increase the battery capacity

## Recipe

<RecipeFor id="wireless_crafting_terminal" />
