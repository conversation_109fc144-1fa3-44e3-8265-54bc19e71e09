---
navigation:
  parent: items-blocks-machines/items-blocks-machines-index.md
  title: Matter Condenser
  icon: condenser
  position: 310
categories:
- machines
item_ids:
- ae2:condenser
---

# The Matter Condenser

<BlockImage id="condenser" scale="8" />

The matter condenser can be used as either a trash can or to create <ItemLink id="matter_ball" />s and
[singularities](singularities.md). It can take any item or fluid or etc. a storage cell can store.

## Settings/Recipes

*   In trash can mode, the matter condenser just voids everything that enters it
*   In matter ball mode the condenser makes <ItemLink id="matter_ball" />s out of whatever you put in it.
    This mode requires that you put a storage component in the top slot of the condenser. Matter balls take 256 items or buckets
    each, so a <ItemLink id="cell_component_1k" /> (which provides 8192 bits of capacity) is more than enough.
*   In matter singularity mode the condenser makes [singularities](singularities.md) out of whatever you put in it.
    This mode requires that you put a storage component in the top slot of the condenser. Singularities take 256,000 items or buckets
    each, so a <ItemLink id="cell_component_64k" /> (which provides 524,288 bits of capacity) is more than enough.

## Recipe

<RecipeFor id="condenser" />
