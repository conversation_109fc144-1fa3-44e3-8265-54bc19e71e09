{
    DataVersion: 3460,
    size: [5, 2, 1],
    data: [
        {pos: [0, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 838568L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 2, channelsWest: 2, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 838568L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsUp: 1, channelsWest: 2, connections: ["up", "west"], missingChannel: 0b, powered: 1b}}, east: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 838568L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 838563L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsEast: 4, channelsUp: 4, connections: ["up", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 838563L, p: 0}, id: "ae2:cable_interface", priority: 0, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [3, 0, 0], state: "ae2:drive{facing:north,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_4k", tag: {}}, item1: {Count: 1b, id: "ae2:item_storage_cell_4k", tag: {}}, item2: {Count: 1b, id: "ae2:item_storage_cell_4k", tag: {}}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 838563L, p: 0}, visual: {cell0: {id: "ae2:item_storage_cell_4k", state: "empty"}, cell1: {id: "ae2:item_storage_cell_4k", state: "empty"}, cell2: {id: "ae2:item_storage_cell_4k", state: "empty"}, online: 1b}}},
        {pos: [4, 0, 0], state: "ae2:interface", nbt: {fuzzy_mode: "IGNORE_ALL", id: "ae2:interface", priority: 0, proxy: {g: 838563L, p: 0}, visual: {}}},
        {pos: [1, 1, 0], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 838568L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 1, channelsEast: 0, connections: ["down", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {filter_type: "ALL", gn: {g: 838568L, p: 0}, id: "ae2:crafting_terminal", sort_by: "NAME", sort_direction: "ASCENDING", spin: 0b, view_mode: "ALL", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [2, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 838563L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsDown: 4, channelsEast: 4, connections: ["down", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {gn: {g: 838563L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 838568L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [3, 1, 0], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 838563L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsDown: 4, channelsWest: 4, connections: ["down", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {filter_type: "ALL", gn: {g: 838563L, p: 0}, id: "ae2:crafting_terminal", sort_by: "NAME", sort_direction: "ASCENDING", spin: 0b, view_mode: "ALL", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [4, 1, 0], state: "minecraft:hopper{enabled:true,facing:down}", nbt: {Items: [], TransferCooldown: 0, id: "minecraft:hopper"}}
    ],
    entities: [],
    palette: [
        "ae2:cable_bus{light_level:0,waterlogged:false}",
        "ae2:drive{facing:north,spin:0}",
        "ae2:interface",
        "ae2:cable_bus{light_level:9,waterlogged:false}",
        "minecraft:hopper{enabled:true,facing:down}"
    ]
}