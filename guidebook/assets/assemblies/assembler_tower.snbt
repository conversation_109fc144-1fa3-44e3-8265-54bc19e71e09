{
    DataVersion: 3337,
    size: [4, 2, 2],
    data: [
        {pos: [0, 0, 0], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 379L, k: -1L, p: 0}, upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}, {Count: 1b, Slot: 2, id: "ae2:speed_card"}, {Count: 1b, Slot: 3, id: "ae2:speed_card"}, {Count: 1b, Slot: 4, id: "ae2:speed_card"}], visual: {}}},
        {pos: [0, 0, 1], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 379L, k: -1L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [1, 0, 0], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 379L, k: -1L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [1, 0, 1], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 379L, k: -1L, p: 0}, upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}, {Count: 1b, Slot: 2, id: "ae2:speed_card"}, {Count: 1b, Slot: 3, id: "ae2:speed_card"}, {Count: 1b, Slot: 4, id: "ae2:speed_card"}], visual: {}}},
        {pos: [2, 0, 0], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 379L, k: -1L, p: 0}, upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}, {Count: 1b, Slot: 2, id: "ae2:speed_card"}, {Count: 1b, Slot: 3, id: "ae2:speed_card"}, {Count: 1b, Slot: 4, id: "ae2:speed_card"}], visual: {}}},
        {pos: [2, 0, 1], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 379L, k: -1L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [3, 0, 0], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 379L, k: -1L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [3, 0, 1], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 379L, k: -1L, p: 0}, upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}, {Count: 1b, Slot: 2, id: "ae2:speed_card"}, {Count: 1b, Slot: 3, id: "ae2:speed_card"}, {Count: 1b, Slot: 4, id: "ae2:speed_card"}], visual: {}}},
        {pos: [0, 1, 0], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 269L, k: -1L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [0, 1, 1], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 269L, k: -1L, p: 0}, upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}, {Count: 1b, Slot: 2, id: "ae2:speed_card"}, {Count: 1b, Slot: 3, id: "ae2:speed_card"}, {Count: 1b, Slot: 4, id: "ae2:speed_card"}], visual: {}}},
        {pos: [1, 1, 0], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 269L, k: -1L, p: 0}, upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}, {Count: 1b, Slot: 2, id: "ae2:speed_card"}, {Count: 1b, Slot: 3, id: "ae2:speed_card"}, {Count: 1b, Slot: 4, id: "ae2:speed_card"}], visual: {}}},
        {pos: [1, 1, 1], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 269L, k: -1L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [2, 1, 0], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 269L, k: -1L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [2, 1, 1], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 269L, k: -1L, p: 0}, upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}, {Count: 1b, Slot: 2, id: "ae2:speed_card"}, {Count: 1b, Slot: 3, id: "ae2:speed_card"}, {Count: 1b, Slot: 4, id: "ae2:speed_card"}], visual: {}}},
        {pos: [3, 1, 0], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 269L, k: -1L, p: 0}, upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}, {Count: 1b, Slot: 2, id: "ae2:speed_card"}, {Count: 1b, Slot: 3, id: "ae2:speed_card"}, {Count: 1b, Slot: 4, id: "ae2:speed_card"}], visual: {}}},
        {pos: [3, 1, 1], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 269L, k: -1L, p: 0}, returnInv: [], sendList: [], visual: {}}}
    ],
    entities: [],
    palette: [
        "ae2:molecular_assembler{powered:true}",
        "ae2:pattern_provider{push_direction:all}"
    ]
}