{
    DataVersion: 3337,
    size: [8, 2, 3],
    data: [
        {pos: [0, 0, 1], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 83825L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 0, channelsSouth: 0, connections: ["south", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {gn: {g: 83825L, k: -1L, p: 0}, id: "ae2:storage_monitor", isLocked: 0b, spin: 2b, visual: {amount: 0L, missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [0, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83825L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 0, channelsNorth: 0, connections: ["north", "east"], missingChannel: 0b, powered: 1b}}, facadeUp: {Count: 1b, id: "ae2:facade", tag: {item: "minecraft:stone"}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83825L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsSouth: 0, connections: ["south"], missingChannel: 0b, powered: 1b}}, east: {craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {g: 83825L, k: -1L, p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", visual: {missingChannel: 1b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83825L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 1b, powered: 1b}}, visual: {}, west: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 83825L, k: -1L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 1b, powered: 1b}}}},
        {pos: [1, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83825L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 0, channelsNorth: 0, channelsSouth: 0, channelsWest: 0, connections: ["north", "south", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {craft_via_redstone: "NO", fuzzy_mode: "IGNORE_ALL", gn: {g: 83825L, k: -1L, p: 0}, id: "ae2:level_emitter", lastReportedValue: 0L, prevState: 1b, redstone_emitter: "HIGH_SIGNAL", reportingValue: 0L, visual: {missingChannel: 0b, on: 1b, powered: 1b}}, visual: {}}},
        {pos: [1, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 250L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 0, channelsNorth: 0, channelsWest: 0, connections: ["north", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {g: 250L, k: -1L, p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", visual: {missingChannel: 1b, powered: 1b}}, visual: {}}},
        {pos: [2, 0, 1], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 250L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 0, channelsSouth: 0, channelsWest: 0, connections: ["south", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {gn: {g: 250L, k: -1L, p: 0}, id: "ae2:pattern_access_terminal", show_pattern_providers: "VISIBLE", spin: 2b, visual: {missingChannel: 1b, powered: 1b}}, visual: {}}},
        {pos: [2, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 250L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 0, channelsNorth: 0, channelsWest: 0, connections: ["north", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {fuzzy_mode: "IGNORE_ALL", gn: {g: 250L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 1b, powered: 1b}}, visual: {}}},
        {pos: [3, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 250L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 0, channelsSouth: 0, connections: ["south", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {freq: 0s, gn: {g: 250L, k: -1L, p: 0}, id: "ae2:redstone_p2p_tunnel", output: 0b, power: 0, visual: {missingChannel: 1b, powered: 1b}}, visual: {}}},
        {pos: [3, 0, 1], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 250L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 0, channelsNorth: 0, channelsSouth: 0, channelsWest: 0, connections: ["north", "south", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {filter_type: "ALL", gn: {g: 250L, k: -1L, p: 0}, id: "ae2:terminal", sort_by: "NAME", sort_direction: "ASCENDING", spin: 2b, view_mode: "ALL", visual: {missingChannel: 1b, powered: 1b}}, visual: {}}},
        {pos: [3, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 250L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 0, channelsNorth: 0, channelsWest: 0, connections: ["north", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 250L, k: -1L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 1b, powered: 1b}}, visual: {}}},
        {pos: [4, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 250L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 0, channelsSouth: 0, channelsWest: 0, connections: ["south", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {freq: 0s, gn: {g: 250L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 212L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 1b, powered: 1b}}, visual: {}}},
        {pos: [4, 0, 1], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 250L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 0, channelsNorth: 0, channelsSouth: 0, channelsWest: 0, connections: ["north", "south", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {filter_type: "ALL", gn: {g: 250L, k: -1L, p: 0}, id: "ae2:crafting_terminal", sort_by: "NAME", sort_direction: "ASCENDING", spin: 2b, view_mode: "ALL", visual: {missingChannel: 1b, powered: 1b}}, visual: {}}},
        {pos: [4, 0, 2], state: "ae2:cable_bus{light_level:15,waterlogged:false}", nbt: {cable: {gn: {g: 250L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 0, channelsNorth: 0, channelsWest: 0, connections: ["north", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {gn: {g: 250L, k: -1L, p: 0}, id: "ae2:dark_monitor", spin: 2b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [5, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 250L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 0, channelsSouth: 0, channelsWest: 0, connections: ["south", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {gn: {g: 250L, k: -1L, p: 0}, id: "ae2:cable_energy_acceptor", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [5, 0, 1], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 250L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 0, channelsNorth: 0, channelsSouth: 0, channelsWest: 0, connections: ["north", "south", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {filter_type: "ALL", gn: {g: 250L, k: -1L, p: 0}, id: "ae2:pattern_encoding_terminal", mode: "CRAFTING", sort_by: "NAME", sort_direction: "ASCENDING", spin: 2b, substitute: 0b, substituteFluids: 1b, view_mode: "ALL", visual: {missingChannel: 1b, powered: 1b}}, visual: {}}},
        {pos: [5, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 250L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 0, channelsNorth: 0, channelsWest: 0, connections: ["north", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {id: "ae2:cable_anchor", visual: {}}, visual: {}}},
        {pos: [6, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 250L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 0, channelsSouth: 0, channelsWest: 0, connections: ["south", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {fuzzy_mode: "IGNORE_ALL", gn: {g: 250L, k: -1L, p: 0}, id: "ae2:cable_interface", priority: 0, visual: {missingChannel: 1b, powered: 1b}}, visual: {}}},
        {pos: [6, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 250L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 0, channelsNorth: 0, channelsSouth: 0, channelsWest: 0, connections: ["north", "south", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {fuzzy_mode: "IGNORE_ALL", gn: {g: 250L, k: -1L, p: 0}, id: "ae2:formation_plane", place_block: "YES", priority: 0, visual: {missingChannel: 1b, powered: 1b}}, visual: {}}},
        {pos: [6, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 250L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 0, channelsNorth: 0, channelsWest: 0, connections: ["north", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 1, id: "ae2:cable_bus", up: {gn: {g: 250L, k: -1L, p: 0}, id: "ae2:toggle_bus", outer: {g: 254L, k: -1L, p: 0}, visual: {missingChannel: 0b, on: 0b, powered: 1b}}, visual: {}}},
        {pos: [7, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 250L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsSouth: 0, channelsWest: 0, connections: ["south", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {blocking_mode: "NO", gn: {g: 250L, k: -1L, p: 0}, id: "ae2:cable_pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, returnInv: [], sendList: [], visual: {missingChannel: 1b, powered: 1b}}, visual: {}}},
        {pos: [7, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 250L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsNorth: 0, channelsSouth: 0, channelsWest: 0, connections: ["north", "south", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {gn: {g: 250L, k: -1L, p: 0}, id: "ae2:annihilation_plane", visual: {missingChannel: 1b, powered: 1b}}, visual: {}}},
        {pos: [7, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 250L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsNorth: 0, channelsWest: 0, connections: ["north", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {gn: {g: 250L, k: -1L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 252L, k: -1L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
    ],
    entities: [],
    palette: [
        "ae2:cable_bus{light_level:9,waterlogged:false}",
        "ae2:cable_bus{light_level:0,waterlogged:false}",
        "ae2:cable_bus{light_level:15,waterlogged:false}",
        "ae2:creative_energy_cell"
    ]
}