{
    DataVersion: 3337,
    size: [8, 2, 5],
    data: [
        {pos: [0, 0, 0], state: "minecraft:redstone_lamp{lit:true}"},
        {pos: [0, 0, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27424L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsEast: 5, channelsUp: 5, connections: ["up", "east"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 0, 0], state: "minecraft:redstone_wire{east:side,north:none,power:15,south:none,west:side}"},
        {pos: [1, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83704L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsEast: 5, channelsUp: 5, connections: ["up", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 0, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27424L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsEast: 5, channelsWest: 5, connections: ["west", "east"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27429L, k: -1L, p: 2}, id: "ae2:red_smart_cable", visual: {channelsEast: 2, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {freq: 2001s, gn: {g: 27429L, k: -1L, p: 2}, id: "ae2:redstone_p2p_tunnel", output: 1b, power: 15, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [2, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83704L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsEast: 5, channelsWest: 5, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 0, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 4118L, k: -1L, p: 1}, id: "ae2:red_smart_cable", visual: {channelsEast: 0, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {freq: -32479s, gn: {g: 4118L, k: -1L, p: 1}, id: "ae2:me_p2p_tunnel", outer: {g: 27424L, k: -1L, p: 1}, output: 1b, visual: {missingChannel: 1b, powered: 1b}}}},
        {pos: [3, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27425L, k: -1L, p: 2}, id: "ae2:orange_smart_cable", visual: {channelsEast: 2, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {freq: 24045s, gn: {g: 27425L, k: -1L, p: 2}, id: "ae2:me_p2p_tunnel", outer: {g: 27429L, k: -1L, p: 2}, output: 1b, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [3, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 4127L, k: -1L, p: 1}, id: "ae2:orange_smart_cable", visual: {channelsEast: 2, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {freq: 17678s, gn: {g: 4127L, k: -1L, p: 1}, id: "ae2:me_p2p_tunnel", outer: {g: 83704L, k: -1L, p: 1}, output: 1b, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [3, 0, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 4113L, k: -1L, p: 1}, id: "ae2:orange_smart_cable", visual: {channelsEast: 2, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {freq: 5234s, gn: {g: 4113L, k: -1L, p: 1}, id: "ae2:me_p2p_tunnel", outer: {g: 4118L, k: -1L, p: 1}, output: 1b, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [4, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27425L, k: -1L, p: 2}, id: "ae2:orange_smart_cable", visual: {channelsWest: 2, connections: ["west"], missingChannel: 0b, powered: 1b}}, east: {freq: 24045s, gn: {g: 27425L, k: -1L, p: 2}, id: "ae2:me_p2p_tunnel", outer: {g: 27429L, k: -1L, p: 2}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 4127L, k: -1L, p: 1}, id: "ae2:orange_smart_cable", visual: {channelsWest: 2, connections: ["west"], missingChannel: 0b, powered: 1b}}, east: {freq: 17678s, gn: {g: 4127L, k: -1L, p: 1}, id: "ae2:me_p2p_tunnel", outer: {g: 83704L, k: -1L, p: 1}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 0, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 4113L, k: -1L, p: 1}, id: "ae2:orange_smart_cable", visual: {channelsWest: 2, connections: ["west"], missingChannel: 0b, powered: 1b}}, east: {freq: 5234s, gn: {g: 4113L, k: -1L, p: 1}, id: "ae2:me_p2p_tunnel", outer: {g: 4118L, k: -1L, p: 1}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27429L, k: -1L, p: 2}, id: "ae2:red_smart_cable", visual: {channelsWest: 2, connections: ["west"], missingChannel: 0b, powered: 1b}}, east: {freq: 2001s, gn: {g: 27429L, k: -1L, p: 2}, id: "ae2:redstone_p2p_tunnel", output: 0b, power: 15, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83704L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsEast: 5, channelsWest: 5, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 0, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 4118L, k: -1L, p: 1}, id: "ae2:red_smart_cable", visual: {channelsWest: 0, connections: ["west"], missingChannel: 0b, powered: 1b}}, east: {freq: -32479s, gn: {g: 4118L, k: -1L, p: 1}, id: "ae2:me_p2p_tunnel", outer: {g: 27421L, k: -1L, p: 1}, output: 0b, visual: {missingChannel: 1b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 0, 0], state: "minecraft:redstone_wire{east:side,north:none,power:15,south:none,west:side}"},
        {pos: [6, 0, 2], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 83704L, k: -1L, p: 1}, visual: {}}},
        {pos: [6, 0, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27421L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsEast: 0, channelsWest: 0, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 0, 0], state: "minecraft:redstone_block"},
        {pos: [7, 0, 4], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 27421L, k: -1L, p: 1}, visual: {}}},
        {pos: [0, 1, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27424L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsDown: 5, connections: ["down"], missingChannel: 0b, powered: 0b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 27424L, k: -1L, p: 2}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 0b}}, hasRedstone: 1, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 27424L, k: -1L, p: 2}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 0b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 27424L, k: -1L, p: 2}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 0b}}, up: {fuzzy_mode: "IGNORE_ALL", gn: {g: 27424L, k: -1L, p: 2}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 0b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 27424L, k: -1L, p: 2}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 0b}}}},
        {pos: [1, 1, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83704L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsDown: 5, connections: ["down"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83704L, k: -1L, p: 2}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 1, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83704L, k: -1L, p: 2}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83704L, k: -1L, p: 2}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, up: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83704L, k: -1L, p: 2}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83704L, k: -1L, p: 2}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}}
    ],
    entities: [],
    palette: [
        "minecraft:redstone_lamp{lit:true}",
        "minecraft:redstone_block",
        "minecraft:redstone_wire{east:side,north:none,power:15,south:none,west:side}",
        "ae2:cable_bus{light_level:0,waterlogged:false}",
        "ae2:controller{state:online,type:block}"
    ]
}