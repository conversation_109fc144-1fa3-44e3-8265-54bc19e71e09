{
    DataVersion: 3337,
    size: [5, 2, 3],
    data: [
        {pos: [0, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 17050L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 1, channelsWest: 1, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", south: {access: "READ_WRITE", config: [{"#": 0L, "#c": "ae2:i", id: "minecraft:amethyst_shard"}], filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 17050L, k: -1L, p: 0}, id: "ae2:storage_bus", priority: 1, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [0, 0, 1], state: "minecraft:barrel{facing:west,open:false}", nbt: {Items: [{Count: 64b, Slot: 0b, id: "minecraft:amethyst_shard"}, {Count: 64b, Slot: 1b, id: "minecraft:amethyst_shard"}, {Count: 64b, Slot: 2b, id: "minecraft:amethyst_shard"}, {Count: 64b, Slot: 3b, id: "minecraft:amethyst_shard"}, {Count: 64b, Slot: 4b, id: "minecraft:amethyst_shard"}, {Count: 64b, Slot: 5b, id: "minecraft:amethyst_shard"}, {Count: 64b, Slot: 6b, id: "minecraft:amethyst_shard"}, {Count: 64b, Slot: 7b, id: "minecraft:amethyst_shard"}, {Count: 64b, Slot: 8b, id: "minecraft:amethyst_shard"}, {Count: 64b, Slot: 9b, id: "minecraft:amethyst_shard"}, {Count: 64b, Slot: 10b, id: "minecraft:amethyst_shard"}, {Count: 64b, Slot: 11b, id: "minecraft:amethyst_shard"}, {Count: 64b, Slot: 12b, id: "minecraft:amethyst_shard"}, {Count: 64b, Slot: 13b, id: "minecraft:amethyst_shard"}, {Count: 64b, Slot: 14b, id: "minecraft:amethyst_shard"}, {Count: 64b, Slot: 15b, id: "minecraft:amethyst_shard"}, {Count: 64b, Slot: 16b, id: "minecraft:amethyst_shard"}, {Count: 64b, Slot: 17b, id: "minecraft:amethyst_shard"}], id: "minecraft:barrel"}},
        {pos: [1, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 17047L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 2, channelsSouth: 2, connections: ["south", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {gn: {g: 17047L, k: -1L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 17050L, k: -1L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [1, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 17047L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsNorth: 2, connections: ["north"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {gn: {g: 17047L, k: -1L, p: 0}, id: "ae2:annihilation_plane", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {access: "READ_WRITE", config: [{"#": 0L, "#c": "ae2:i", id: "minecraft:amethyst_shard"}], filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 17047L, k: -1L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [2, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 17047L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 2, channelsSouth: 2, channelsWest: 2, connections: ["south", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {gn: {g: 17047L, k: -1L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 1720L, k: -1L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [2, 0, 1], state: "ae2:growth_accelerator{facing:south,powered:true}", nbt: {id: "ae2:growth_accelerator", proxy: {g: 17047L, k: -1L, p: 0}, visual: {}}},
        {pos: [2, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 17047L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 2, channelsNorth: 2, connections: ["north", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 0, 0], state: "ae2:growth_accelerator{facing:west,powered:true}", nbt: {id: "ae2:growth_accelerator", proxy: {g: 17047L, k: -1L, p: 0}, visual: {}}},
        {pos: [3, 0, 1], state: "minecraft:budding_amethyst"},
        {pos: [3, 0, 2], state: "ae2:growth_accelerator{facing:east,powered:true}", nbt: {id: "ae2:growth_accelerator", proxy: {g: 17047L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 17047L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsSouth: 2, channelsWest: 2, connections: ["south", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 0, 1], state: "ae2:growth_accelerator{facing:north,powered:true}", nbt: {id: "ae2:growth_accelerator", proxy: {g: 17047L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 17047L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsNorth: 2, channelsWest: 2, connections: ["north", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 1, 1], state: "minecraft:smooth_stone"},
        {pos: [2, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 1720L, k: -1L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsDown: 2, channelsSouth: 2, connections: ["down", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 1720L, k: -1L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsNorth: 2, connections: ["north"], missingChannel: 0b, powered: 1b}}, east: {Enchantments: [{id: "minecraft:silk_touch", lvl: 1s}], gn: {g: 1720L, k: -1L, p: 0}, id: "ae2:annihilation_plane", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {config: [{"#": 0L, "#c": "ae2:i", id: "minecraft:amethyst_cluster"}], fuzzy_mode: "IGNORE_ALL", gn: {g: 1720L, k: -1L, p: 0}, id: "ae2:formation_plane", place_block: "YES", priority: 0, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [3, 1, 1], state: "minecraft:large_amethyst_bud{facing:up,waterlogged:false}"}
    ],
    entities: [],
    palette: [
        "minecraft:budding_amethyst",
        "minecraft:smooth_stone",
        "minecraft:large_amethyst_bud{facing:up,waterlogged:false}",
        "ae2:cable_bus{light_level:0,waterlogged:false}",
        "minecraft:barrel{facing:west,open:false}",
        "ae2:growth_accelerator{facing:south,powered:true}",
        "ae2:growth_accelerator{facing:west,powered:true}",
        "ae2:growth_accelerator{facing:east,powered:true}",
        "ae2:growth_accelerator{facing:north,powered:true}"
    ]
}