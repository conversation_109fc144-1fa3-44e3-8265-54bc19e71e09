{
    DataVersion: 3337,
    size: [2, 1, 3],
    data: [
        {pos: [0, 0, 0], state: "ae2:cable_bus{light_level:15,waterlogged:false}", nbt: {cable: {gn: {g: 135983L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 0, channelsSouth: 0, connections: ["south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {gn: {g: 135983L, k: -1L, p: 0}, id: "ae2:dark_monitor", spin: 3b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [0, 0, 1], state: "ae2:cable_bus{light_level:15,waterlogged:false}", nbt: {cable: {gn: {g: 135983L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsNorth: 0, channelsSouth: 0, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {gn: {g: 135983L, k: -1L, p: 0}, id: "ae2:semi_dark_monitor", spin: 3b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [0, 0, 2], state: "ae2:cable_bus{light_level:15,waterlogged:false}", nbt: {cable: {gn: {g: 135983L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsNorth: 0, connections: ["north"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {gn: {g: 135983L, k: -1L, p: 0}, id: "ae2:monitor", spin: 3b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [1, 0, 0], state: "ae2:cable_bus{light_level:15,waterlogged:false}", nbt: {cable: {gn: {g: 135983L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 0, channelsSouth: 0, connections: ["south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {gn: {g: 135983L, k: -1L, p: 0}, id: "ae2:dark_monitor", spin: 3b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [1, 0, 1], state: "ae2:cable_bus{light_level:15,waterlogged:false}", nbt: {cable: {gn: {g: 135983L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 0, channelsSouth: 0, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {gn: {g: 135983L, k: -1L, p: 0}, id: "ae2:semi_dark_monitor", spin: 3b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [1, 0, 2], state: "ae2:cable_bus{light_level:15,waterlogged:false}", nbt: {cable: {gn: {g: 135983L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 0, connections: ["north"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {gn: {g: 135983L, k: -1L, p: 0}, id: "ae2:monitor", spin: 3b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}}
    ],
    entities: [],
    palette: [
        "ae2:cable_bus{light_level:15,waterlogged:false}"
    ]
}