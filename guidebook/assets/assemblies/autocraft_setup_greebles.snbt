{
    DataVersion: 3337,
    size: [10, 3, 4],
    data: [
        {pos: [0, 0, 2], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 26988L, k: -1L, p: 0}, upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}, {Count: 1b, Slot: 2, id: "ae2:speed_card"}, {Count: 1b, Slot: 3, id: "ae2:speed_card"}, {Count: 1b, Slot: 4, id: "ae2:speed_card"}], visual: {}}},
        {pos: [0, 0, 3], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", patterns: [{Count: 1b, Slot: 0, id: "ae2:crafting_pattern", tag: {in: [{Count: 1b, id: "minecraft:oak_planks"}, {}, {}, {Count: 1b, id: "minecraft:oak_planks"}, {}, {}, {}, {}, {}], out: {Count: 4b, id: "minecraft:stick"}, recipe: "minecraft:stick", substitute: 0b, substituteFluids: 1b}}, {Count: 1b, Slot: 1, id: "ae2:crafting_pattern", tag: {in: [{Count: 1b, id: "minecraft:iron_ingot"}, {}, {Count: 1b, id: "minecraft:iron_ingot"}, {Count: 1b, id: "minecraft:iron_ingot"}, {Count: 1b, id: "minecraft:chest"}, {Count: 1b, id: "minecraft:iron_ingot"}, {}, {Count: 1b, id: "minecraft:iron_ingot"}, {}], out: {Count: 1b, id: "minecraft:hopper"}, recipe: "minecraft:hopper", substitute: 0b, substituteFluids: 1b}}], priority: 0, proxy: {g: 26988L, k: -1L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [1, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 26988L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 4, channelsSouth: 4, connections: ["south", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 26988L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 4, channelsSouth: 4, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 0, 2], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", patterns: [{Count: 1b, Slot: 0, id: "ae2:crafting_pattern", tag: {in: [{Count: 1b, id: "minecraft:iron_ingot"}, {Count: 1b, id: "minecraft:iron_ingot"}, {Count: 1b, id: "minecraft:iron_ingot"}, {}, {Count: 1b, id: "minecraft:stick"}, {}, {}, {Count: 1b, id: "minecraft:stick"}, {}], out: {Count: 1b, id: "minecraft:iron_pickaxe", tag: {Damage: 0}}, recipe: "minecraft:iron_pickaxe", substitute: 0b, substituteFluids: 1b}}], priority: 0, proxy: {g: 26988L, k: -1L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [1, 0, 3], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 26988L, k: -1L, p: 0}, upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}, {Count: 1b, Slot: 2, id: "ae2:speed_card"}, {Count: 1b, Slot: 3, id: "ae2:speed_card"}, {Count: 1b, Slot: 4, id: "ae2:speed_card"}], visual: {}}},
        {pos: [2, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 26988L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 4, channelsWest: 4, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 26988L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 6, channelsSouth: 2, channelsWest: 4, connections: ["south", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 26988L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 2, channelsSouth: 2, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 0, 2], state: "ae2:1k_crafting_storage{formed:true,powered:true}", nbt: {core: 1b, crafting_scheduling_mode: "ANY", id: "ae2:crafting_storage", inventory: [], proxy: {g: 26988L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 0, 0], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 26988L, k: -1L, p: 0}, visual: {}}},
        {pos: [5, 0, 0], state: "ae2:drive{facing:north,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {amts: [L; 384L, 127L, 385L, 896L, 823L], ic: 2615L, keys: [{"#c": "ae2:i", id: "minecraft:iron_ingot"}, {"#c": "ae2:i", id: "minecraft:coal"}, {"#c": "ae2:i", id: "minecraft:cobblestone"}, {"#c": "ae2:i", id: "minecraft:oak_planks"}, {"#c": "ae2:i", id: "ae2:certus_quartz_crystal"}]}}, item1: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item2: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item3: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item4: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item5: {Count: 1b, id: "ae2:fluid_storage_cell_1k", tag: {}}, item6: {}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 26988L, k: -1L, p: 0}, visual: {cell0: {id: "ae2:item_storage_cell_1k", state: "not_empty"}, cell1: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell2: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell3: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell4: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell5: {id: "ae2:fluid_storage_cell_1k", state: "empty"}, online: 1b}}},
        {pos: [5, 0, 2], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 1b, crafting_scheduling_mode: "ANY", id: "ae2:crafting_unit", inventory: [], proxy: {g: 26988L, k: -1L, p: 0}, visual: {}}},
        {pos: [5, 0, 3], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_unit", proxy: {g: 26988L, k: -1L, p: 0}, visual: {}}},
        {pos: [6, 0, 0], state: "ae2:energy_acceptor", nbt: {id: "ae2:energy_acceptor", internalCurrentPower: 0.0d, proxy: {g: 26988L, k: -1L, p: 0}, visual: {}}},
        {pos: [7, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 26988L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 1, channelsSouth: 1, channelsUp: 2, channelsWest: 0, connections: ["up", "south", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 26988L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 1, channelsSouth: 1, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 26988L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsEast: 1, channelsNorth: 1, channelsUp: 0, connections: ["up", "north", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 0, 0], state: "ae2:pattern_provider{push_direction:up}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", patterns: [{Count: 1b, Slot: 0, id: "ae2:processing_pattern", tag: {in: [{"#": 8L, "#c": "ae2:i", id: "ae2:certus_quartz_dust"}, {"#": 1L, "#c": "ae2:i", id: "minecraft:coal"}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}], out: [{"#": 8L, "#c": "ae2:i", id: "ae2:silicon"}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}]}}], priority: 0, proxy: {g: 26988L, k: -1L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [8, 0, 2], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", patterns: [{Count: 1b, Slot: 0, id: "ae2:processing_pattern", tag: {in: [{"#": 1L, "#c": "ae2:i", id: "ae2:certus_quartz_crystal"}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}], out: [{"#": 1L, "#c": "ae2:i", id: "ae2:certus_quartz_dust"}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}]}}], priority: 0, proxy: {g: 26988L, k: -1L, p: 2}, returnInv: [], sendList: [], visual: {}}},
        {pos: [9, 0, 0], state: "minecraft:hopper{enabled:true,facing:west}", nbt: {Items: [], TransferCooldown: 0, id: "minecraft:hopper"}},
        {pos: [9, 0, 2], state: "ae2:inscriber{facing:east,spin:1,waterlogged:false}", nbt: {auto_export: "NO", id: "ae2:inscriber", inscriber_separate_sides: "YES", internalCurrentPower: 0.0d, inv: {item0: {}, item1: {}, item2: {}, item3: {}}, proxy: {g: 26988L, k: -1L, p: 2}, upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}, {Count: 1b, Slot: 2, id: "ae2:speed_card"}], visual: {smash: 0b}}},
        {pos: [0, 1, 2], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", patterns: [{Count: 1b, Slot: 0, id: "ae2:crafting_pattern", tag: {in: [{Count: 1b, id: "minecraft:cobblestone"}, {Count: 1b, id: "minecraft:cobblestone"}, {Count: 1b, id: "minecraft:cobblestone"}, {}, {Count: 1b, id: "minecraft:stick"}, {}, {}, {Count: 1b, id: "minecraft:stick"}, {}], out: {Count: 1b, id: "minecraft:stone_pickaxe", tag: {Damage: 0}}, recipe: "minecraft:stone_pickaxe", substitute: 0b, substituteFluids: 1b}}], priority: 0, proxy: {g: 26988L, k: -1L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [0, 1, 3], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 26988L, k: -1L, p: 0}, upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}, {Count: 1b, Slot: 2, id: "ae2:speed_card"}, {Count: 1b, Slot: 3, id: "ae2:speed_card"}, {Count: 1b, Slot: 4, id: "ae2:speed_card"}], visual: {}}},
        {pos: [1, 1, 2], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 26988L, k: -1L, p: 0}, upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}, {Count: 1b, Slot: 2, id: "ae2:speed_card"}, {Count: 1b, Slot: 3, id: "ae2:speed_card"}, {Count: 1b, Slot: 4, id: "ae2:speed_card"}], visual: {}}},
        {pos: [1, 1, 3], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", patterns: [{Count: 1b, Slot: 0, id: "ae2:crafting_pattern", tag: {in: [{Count: 1b, id: "minecraft:oak_planks"}, {Count: 1b, id: "minecraft:oak_planks"}, {Count: 1b, id: "minecraft:oak_planks"}, {Count: 1b, id: "minecraft:oak_planks"}, {}, {Count: 1b, id: "minecraft:oak_planks"}, {Count: 1b, id: "minecraft:oak_planks"}, {Count: 1b, id: "minecraft:oak_planks"}, {Count: 1b, id: "minecraft:oak_planks"}], out: {Count: 1b, id: "minecraft:chest"}, recipe: "minecraft:chest", substitute: 0b, substituteFluids: 1b}}], priority: 0, proxy: {g: 26988L, k: -1L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [3, 1, 2], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_unit", proxy: {g: 26988L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 1, 0], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 26988L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 5, channelsEast: 4, connections: ["down", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {blankPattern: [{Count: 53b, Slot: 0, id: "ae2:blank_pattern"}], encodedInputs: [{"#": 1L, "#c": "ae2:i", id: "minecraft:iron_ingot"}, {}, {"#": 1L, "#c": "ae2:i", id: "minecraft:iron_ingot"}, {"#": 1L, "#c": "ae2:i", id: "minecraft:iron_ingot"}, {"#": 1L, "#c": "ae2:i", id: "minecraft:chest"}, {"#": 1L, "#c": "ae2:i", id: "minecraft:iron_ingot"}, {}, {"#": 1L, "#c": "ae2:i", id: "minecraft:iron_ingot"}], filter_type: "ALL", gn: {g: 26988L, k: -1L, p: 0}, id: "ae2:pattern_encoding_terminal", mode: "CRAFTING", sort_by: "NAME", sort_direction: "ASCENDING", spin: 0b, substitute: 0b, substituteFluids: 1b, view_mode: "ALL", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [4, 1, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 26988L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 1, channelsWest: 1, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 1, 0], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 26988L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 0, channelsEast: 3, channelsWest: 4, connections: ["down", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {filter_type: "ALL", gn: {g: 26988L, k: -1L, p: 0}, id: "ae2:crafting_terminal", sort_by: "NAME", sort_direction: "ASCENDING", spin: 0b, view_mode: "ALL", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [5, 1, 2], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_unit", proxy: {g: 26988L, k: -1L, p: 0}, visual: {}}},
        {pos: [5, 1, 3], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_unit", proxy: {g: 154L, k: -1L, p: 0}, visual: {}}},
        {pos: [6, 1, 0], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 154L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 0, channelsEast: 2, channelsWest: 3, connections: ["down", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {gn: {g: 154L, k: -1L, p: 0}, id: "ae2:pattern_access_terminal", show_pattern_providers: "VISIBLE", spin: 0b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [7, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 154L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 2, channelsWest: 2, connections: ["down", "west"], missingChannel: 0b, powered: 1b}}, east: {gn: {g: 154L, k: -1L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 159L, k: -1L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 1, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 154L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsDown: 0, connections: ["down"], missingChannel: 0b, powered: 1b}}, east: {gn: {g: 154L, k: -1L, p: 2}, id: "ae2:quartz_fiber", outer: {g: 27383L, k: -1L, p: 2}, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 159L, k: -1L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsUp: 3, channelsWest: 3, connections: ["up", "west"], missingChannel: 0b, powered: 1b}}, down: {fuzzy_mode: "IGNORE_ALL", gn: {g: 159L, k: -1L, p: 0}, id: "ae2:cable_interface", priority: 0, visual: {missingChannel: 0b, powered: 1b}}, east: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 159L, k: -1L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 1, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27383L, k: -1L, p: 2}, id: "ae2:green_smart_cable", visual: {channelsEast: 2, channelsWest: 2, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 27383L, k: -1L, p: 2}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 1, 0], state: "minecraft:furnace{facing:north,lit:false}", nbt: {BurnTime: 0s, CookTime: 0s, CookTimeTotal: 200s, Items: [], RecipesUsed: {"ae2:smelting/silicon_from_certus_quartz_dust": 8}, id: "minecraft:furnace"}},
        {pos: [9, 1, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27383L, k: -1L, p: 2}, id: "ae2:green_smart_cable", visual: {channelsWest: 2, connections: ["west"], missingChannel: 0b, powered: 1b}}, down: {fuzzy_mode: "IGNORE_ALL", gn: {g: 27383L, k: -1L, p: 2}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 2, 2], state: "ae2:crafting_monitor{facing:north,formed:true,powered:true,spin:0}", nbt: {core: 0b, id: "ae2:crafting_monitor", paintedColor: 11b, proxy: {g: 154L, k: -1L, p: 0}, visual: {}}},
        {pos: [5, 2, 3], state: "ae2:16k_crafting_storage{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_storage", proxy: {g: 154L, k: -1L, p: 0}, visual: {}}},
        {pos: [8, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 159L, k: -1L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsDown: 3, channelsEast: 3, connections: ["down", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 159L, k: -1L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsWest: 3, connections: ["west"], missingChannel: 0b, powered: 1b}}, down: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 159L, k: -1L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}}
    ],
    entities: [],
    palette: [
        "ae2:molecular_assembler{powered:true}",
        "ae2:pattern_provider{push_direction:all}",
        "ae2:cable_bus{light_level:0,waterlogged:false}",
        "ae2:1k_crafting_storage{formed:true,powered:true}",
        "ae2:controller{state:online,type:block}",
        "ae2:drive{facing:north,spin:0}",
        "ae2:crafting_accelerator{formed:true,powered:true}",
        "ae2:energy_acceptor",
        "ae2:pattern_provider{push_direction:up}",
        "minecraft:hopper{enabled:true,facing:west}",
        "ae2:inscriber{facing:east,spin:1,waterlogged:false}",
        "ae2:cable_bus{light_level:9,waterlogged:false}",
        "minecraft:furnace{facing:north,lit:false}",
        "ae2:crafting_monitor{facing:north,formed:true,powered:true,spin:0}",
        "ae2:16k_crafting_storage{formed:true,powered:true}"
    ]
}