{
    DataVersion: 3460,
    size: [9, 3, 3],
    data: [
        {pos: [0, 0, 1], state: "minecraft:hopper{enabled:true,facing:down}", nbt: {Items: [], TransferCooldown: 0, id: "minecraft:hopper"}},
        {pos: [0, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 673493L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 1, channelsUp: 1, connections: ["up", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 0, 1], state: "minecraft:chest{facing:north,type:single,waterlogged:false}", nbt: {Items: [], id: "minecraft:chest"}},
        {pos: [1, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 673493L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 3, channelsUp: 2, channelsWest: 1, connections: ["up", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 673493L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 3, channelsWest: 3, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 0, 1], state: "minecraft:chest{facing:north,type:single,waterlogged:false}", nbt: {Items: [], id: "minecraft:chest"}},
        {pos: [3, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 673493L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 3, channelsWest: 3, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 673493L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsSouth: 2, channelsUp: 2, connections: ["up", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 0, 2], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 673493L, p: 0}, visual: {}}},
        {pos: [5, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 673493L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 4, channelsWest: 4, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 0, 1], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 673493L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [6, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 673493L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 3, channelsNorth: 1, channelsWest: 4, connections: ["north", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 0, 1], state: "ae2:interface", nbt: {fuzzy_mode: "IGNORE_ALL", id: "ae2:interface", priority: 0, proxy: {g: 673493L, p: 0}, visual: {}}},
        {pos: [7, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 673493L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 2, channelsNorth: 1, channelsWest: 3, connections: ["north", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 0, 1], state: "minecraft:chest{facing:north,type:single,waterlogged:false}", nbt: {Items: [], id: "minecraft:chest"}},
        {pos: [8, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 673493L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsUp: 2, channelsWest: 2, connections: ["up", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 1, 1], state: "ae2:interface", nbt: {fuzzy_mode: "IGNORE_ALL", id: "ae2:interface", priority: 0, proxy: {g: 673493L, p: 0}, visual: {}}},
        {pos: [0, 1, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 673493L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 1, channelsEast: 0, channelsNorth: 1, connections: ["down", "north", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 673493L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsSouth: 2, channelsUp: 1, channelsWest: 0, connections: ["up", "south", "west"], missingChannel: 0b, powered: 1b}}, down: {craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {g: 673493L, p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 1, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 673493L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 2, channelsNorth: 2, channelsWest: 0, connections: ["down", "north", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 673493L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 1, connections: ["east"], missingChannel: 0b, powered: 1b}}, down: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 673493L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 1, 1], state: "ae2:drive{facing:north,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {amts: [L; 2L], ic: 2L, keys: [{"#c": "ae2:i", id: "ae2:quartz_block"}]}}, item1: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {amts: [L; 2L], ic: 2L, keys: [{"#c": "ae2:i", id: "ae2:quartz_block"}]}}, item2: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {amts: [L; 2L], ic: 2L, keys: [{"#c": "ae2:i", id: "ae2:quartz_block"}]}}, item3: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {amts: [L; 2L], ic: 2L, keys: [{"#c": "ae2:i", id: "ae2:quartz_block"}]}}, item4: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {amts: [L; 2L], ic: 2L, keys: [{"#c": "ae2:i", id: "ae2:quartz_block"}]}}, item5: {Count: 1b, id: "ae2:fluid_storage_cell_16k", tag: {}}, item6: {Count: 1b, id: "ae2:fluid_storage_cell_16k", tag: {}}, item7: {Count: 1b, id: "ae2:fluid_storage_cell_16k", tag: {}}, item8: {}, item9: {}}, priority: 0, proxy: {g: 673493L, p: 0}, visual: {cell0: {id: "ae2:item_storage_cell_1k", state: "not_empty"}, cell1: {id: "ae2:item_storage_cell_1k", state: "not_empty"}, cell2: {id: "ae2:item_storage_cell_1k", state: "not_empty"}, cell3: {id: "ae2:item_storage_cell_1k", state: "not_empty"}, cell4: {id: "ae2:item_storage_cell_1k", state: "not_empty"}, cell5: {id: "ae2:fluid_storage_cell_16k", state: "empty"}, cell6: {id: "ae2:fluid_storage_cell_16k", state: "empty"}, cell7: {id: "ae2:fluid_storage_cell_16k", state: "empty"}, online: 1b}}},
        {pos: [6, 1, 1], state: "minecraft:hopper{enabled:true,facing:down}", nbt: {Items: [], TransferCooldown: 0, id: "minecraft:hopper"}},
        {pos: [7, 1, 1], state: "minecraft:hopper{enabled:true,facing:down}", nbt: {Items: [], TransferCooldown: 0, id: "minecraft:hopper"}},
        {pos: [8, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 673493L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsSouth: 2, channelsUp: 1, connections: ["up", "south"], missingChannel: 0b, powered: 1b}}, down: {fuzzy_mode: "IGNORE_ALL", gn: {g: 673493L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 1, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 673493L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 2, channelsNorth: 2, connections: ["down", "north"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 2, 1], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 673493L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 1, connections: ["down"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {filter_type: "ALL", gn: {g: 673493L, p: 0}, id: "ae2:crafting_terminal", sort_by: "NAME", sort_direction: "ASCENDING", spin: 0b, view_mode: "ALL", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [8, 2, 1], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 673493L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 1, connections: ["down"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {filter_type: "ALL", gn: {g: 673493L, p: 0}, id: "ae2:crafting_terminal", sort_by: "NAME", sort_direction: "ASCENDING", spin: 0b, view_mode: "ALL", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}}
    ],
    entities: [],
    palette: [
        "minecraft:hopper{enabled:true,facing:down}",
        "ae2:cable_bus{light_level:0,waterlogged:false}",
        "minecraft:chest{facing:north,type:single,waterlogged:false}",
        "ae2:controller{state:online,type:block}",
        "ae2:pattern_provider{push_direction:all}",
        "ae2:interface",
        "ae2:drive{facing:north,spin:0}",
        "ae2:cable_bus{light_level:9,waterlogged:false}"
    ]
}