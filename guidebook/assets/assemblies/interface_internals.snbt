{
    DataVersion: 3465,
    size: [13, 5, 3],
    data: [
        {pos: [1, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsEast: 0, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {craft_via_redstone: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:level_emitter", lastReportedValue: 0L, prevState: 1b, redstone_emitter: "HIGH_SIGNAL", reportingValue: 0L, visual: {missingChannel: 0b, on: 1b, powered: 1b}}, visual: {}}},
        {pos: [2, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsEast: 0, channelsWest: 0, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {craft_via_redstone: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:level_emitter", lastReportedValue: 0L, prevState: 1b, redstone_emitter: "HIGH_SIGNAL", reportingValue: 0L, visual: {missingChannel: 0b, on: 1b, powered: 1b}}, visual: {}}},
        {pos: [3, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsEast: 0, channelsWest: 0, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {craft_via_redstone: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:level_emitter", lastReportedValue: 0L, prevState: 1b, redstone_emitter: "HIGH_SIGNAL", reportingValue: 0L, visual: {missingChannel: 0b, on: 1b, powered: 1b}}, visual: {}}},
        {pos: [4, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsSouth: 0, channelsWest: 0, connections: ["south", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {craft_via_redstone: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:level_emitter", lastReportedValue: 0L, prevState: 1b, redstone_emitter: "HIGH_SIGNAL", reportingValue: 0L, visual: {missingChannel: 0b, on: 1b, powered: 1b}}, visual: {}}},
        {pos: [4, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsEast: 4, channelsNorth: 0, channelsUp: 4, connections: ["up", "north", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 0, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {craft_via_redstone: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:level_emitter", lastReportedValue: 0L, prevState: 1b, redstone_emitter: "HIGH_SIGNAL", reportingValue: 0L, visual: {missingChannel: 0b, on: 1b, powered: 1b}}, visual: {}}},
        {pos: [5, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsEast: 4, channelsWest: 4, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 0, channelsWest: 0, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {craft_via_redstone: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:level_emitter", lastReportedValue: 0L, prevState: 1b, redstone_emitter: "HIGH_SIGNAL", reportingValue: 0L, visual: {missingChannel: 0b, on: 1b, powered: 1b}}, visual: {}}},
        {pos: [6, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsEast: 4, channelsWest: 4, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 0, channelsWest: 0, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {craft_via_redstone: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:level_emitter", lastReportedValue: 0L, prevState: 1b, redstone_emitter: "HIGH_SIGNAL", reportingValue: 0L, visual: {missingChannel: 0b, on: 1b, powered: 1b}}, visual: {}}},
        {pos: [7, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsEast: 4, channelsWest: 4, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 0, channelsWest: 0, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {craft_via_redstone: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:level_emitter", lastReportedValue: 0L, prevState: 1b, redstone_emitter: "HIGH_SIGNAL", reportingValue: 0L, visual: {missingChannel: 0b, on: 1b, powered: 1b}}, visual: {}}},
        {pos: [8, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsEast: 4, channelsWest: 4, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 0, channelsWest: 0, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {craft_via_redstone: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:level_emitter", lastReportedValue: 0L, prevState: 1b, redstone_emitter: "HIGH_SIGNAL", reportingValue: 0L, visual: {missingChannel: 0b, on: 1b, powered: 1b}}, visual: {}}},
        {pos: [9, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsEast: 4, channelsWest: 4, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_cable", visual: {channelsUp: 0, channelsWest: 0, connections: ["up", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsUp: 4, channelsWest: 4, connections: ["up", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsEast: 1, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [2, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsEast: 2, channelsWest: 1, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [3, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsEast: 3, channelsWest: 2, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [4, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsSouth: 4, channelsWest: 3, connections: ["south", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [4, 1, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsDown: 4, channelsNorth: 4, connections: ["down", "north"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 1, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [6, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 2, channelsWest: 1, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [7, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 3, channelsWest: 2, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [8, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 4, channelsWest: 3, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [9, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 5, channelsWest: 4, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [10, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 0, channelsSouth: 5, channelsWest: 5, connections: ["down", "south", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 1, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:fluix_smart_dense_cable", visual: {channelsDown: 4, channelsNorth: 5, channelsUp: 9, connections: ["down", "up", "north"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 2, 1], state: "minecraft:barrel{facing:north,open:false}", nbt: {Items: [], id: "minecraft:barrel"}},
        {pos: [2, 2, 1], state: "minecraft:barrel{facing:north,open:false}", nbt: {Items: [], id: "minecraft:barrel"}},
        {pos: [3, 2, 1], state: "minecraft:barrel{facing:north,open:false}", nbt: {Items: [], id: "minecraft:barrel"}},
        {pos: [4, 2, 1], state: "minecraft:barrel{facing:north,open:false}", nbt: {Items: [], id: "minecraft:barrel"}},
        {pos: [5, 2, 1], state: "minecraft:barrel{facing:north,open:false}", nbt: {Items: [], id: "minecraft:barrel"}},
        {pos: [6, 2, 1], state: "minecraft:barrel{facing:north,open:false}", nbt: {Items: [], id: "minecraft:barrel"}},
        {pos: [7, 2, 1], state: "minecraft:barrel{facing:north,open:false}", nbt: {Items: [], id: "minecraft:barrel"}},
        {pos: [8, 2, 1], state: "minecraft:barrel{facing:north,open:false}", nbt: {Items: [], id: "minecraft:barrel"}},
        {pos: [9, 2, 1], state: "minecraft:barrel{facing:north,open:false}", nbt: {Items: [], id: "minecraft:barrel"}},
        {pos: [10, 2, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsDown: 9, channelsEast: 18, channelsUp: 9, connections: ["down", "up", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [11, 2, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 18, channelsWest: 18, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 3, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsEast: 1, connections: ["east"], missingChannel: 0b, powered: 1b}}, down: {craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 3, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsEast: 2, channelsWest: 1, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 3, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsEast: 3, channelsWest: 2, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 3, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsSouth: 4, channelsWest: 3, connections: ["south", "west"], missingChannel: 0b, powered: 1b}}, down: {craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 3, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsNorth: 4, channelsUp: 4, connections: ["up", "north"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 3, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 1, connections: ["east"], missingChannel: 0b, powered: 1b}}, down: {craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 3, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 2, channelsWest: 1, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 3, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 3, channelsWest: 2, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 3, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 4, channelsWest: 3, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 3, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 5, channelsWest: 4, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 3, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_cable", visual: {channelsSouth: 5, channelsUp: 0, channelsWest: 5, connections: ["up", "south", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 3, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:fluix_smart_dense_cable", visual: {channelsDown: 9, channelsNorth: 5, channelsUp: 4, connections: ["down", "up", "north"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 4, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsEast: 0, connections: ["east"], missingChannel: 0b, powered: 1b}}, down: {craft_via_redstone: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:level_emitter", lastReportedValue: 0L, prevState: 1b, redstone_emitter: "HIGH_SIGNAL", reportingValue: 0L, visual: {missingChannel: 0b, on: 1b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 4, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsEast: 0, channelsWest: 0, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {craft_via_redstone: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:level_emitter", lastReportedValue: 0L, prevState: 1b, redstone_emitter: "HIGH_SIGNAL", reportingValue: 0L, visual: {missingChannel: 0b, on: 1b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 4, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsEast: 0, channelsWest: 0, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {craft_via_redstone: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:level_emitter", lastReportedValue: 0L, prevState: 1b, redstone_emitter: "HIGH_SIGNAL", reportingValue: 0L, visual: {missingChannel: 0b, on: 1b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 4, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsSouth: 0, channelsWest: 0, connections: ["south", "west"], missingChannel: 0b, powered: 1b}}, down: {craft_via_redstone: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:level_emitter", lastReportedValue: 0L, prevState: 1b, redstone_emitter: "HIGH_SIGNAL", reportingValue: 0L, visual: {missingChannel: 0b, on: 1b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 4, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsDown: 4, channelsEast: 4, channelsNorth: 0, connections: ["down", "north", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 4, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 0, connections: ["east"], missingChannel: 0b, powered: 1b}}, down: {craft_via_redstone: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:level_emitter", lastReportedValue: 0L, prevState: 1b, redstone_emitter: "HIGH_SIGNAL", reportingValue: 0L, visual: {missingChannel: 0b, on: 1b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 4, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsEast: 4, channelsWest: 4, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 4, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 0, channelsWest: 0, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {craft_via_redstone: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:level_emitter", lastReportedValue: 0L, prevState: 1b, redstone_emitter: "HIGH_SIGNAL", reportingValue: 0L, visual: {missingChannel: 0b, on: 1b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 4, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsEast: 4, channelsWest: 4, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 4, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 0, channelsWest: 0, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {craft_via_redstone: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:level_emitter", lastReportedValue: 0L, prevState: 1b, redstone_emitter: "HIGH_SIGNAL", reportingValue: 0L, visual: {missingChannel: 0b, on: 1b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 4, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsEast: 4, channelsWest: 4, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 4, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 0, channelsWest: 0, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {craft_via_redstone: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:level_emitter", lastReportedValue: 0L, prevState: 1b, redstone_emitter: "HIGH_SIGNAL", reportingValue: 0L, visual: {missingChannel: 0b, on: 1b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 4, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsEast: 4, channelsWest: 4, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 4, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 0, channelsWest: 0, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {craft_via_redstone: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:level_emitter", lastReportedValue: 0L, prevState: 1b, redstone_emitter: "HIGH_SIGNAL", reportingValue: 0L, visual: {missingChannel: 0b, on: 1b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 4, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsEast: 4, channelsWest: 4, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 4, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 0, channelsWest: 0, connections: ["down", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 4, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:cyan_smart_cable", visual: {channelsDown: 4, channelsWest: 4, connections: ["down", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}}
    ],
    entities: [],
    palette: [
        "ae2:cable_bus{light_level:0,waterlogged:false}",
        "minecraft:barrel{facing:north,open:false}"
    ]
}