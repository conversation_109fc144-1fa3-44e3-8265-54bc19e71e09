{
    DataVersion: 3460,
    size: [6, 1, 2],
    data: [
        {pos: [0, 0, 0], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 466998L, p: 0}, visual: {}}},
        {pos: [1, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 466998L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 6, channelsWest: 8, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {g: 466998L, p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 1, id: "ae2:cable_bus", up: {craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {g: 466998L, p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [2, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 466998L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 2, channelsSouth: 4, channelsWest: 6, connections: ["south", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 466998L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 4, connections: ["north"], missingChannel: 0b, powered: 1b}}, down: {craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {g: 466998L, p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", visual: {missingChannel: 0b, powered: 1b}}, east: {craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {g: 466998L, p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 1, id: "ae2:cable_bus", up: {craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {g: 466998L, p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {g: 466998L, p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [3, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 466998L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 0, channelsWest: 2, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {g: 466998L, p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {g: 466998L, p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [4, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 466998L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 0, channelsWest: 0, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 0, 0], state: "ae2:drive{facing:south,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {}, item1: {}, item2: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item3: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 466998L, p: 0}, visual: {cell2: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell3: {id: "ae2:item_storage_cell_1k", state: "empty"}, online: 0b}}}
    ],
    entities: [],
    palette: [
        "ae2:controller{state:online,type:block}",
        "ae2:cable_bus{light_level:0,waterlogged:false}",
        "ae2:drive{facing:south,spin:0}"
    ]
}