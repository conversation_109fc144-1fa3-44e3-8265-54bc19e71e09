{
    DataVersion: 3337,
    size: [12, 7, 5],
    data: [
        {pos: [0, 0, 0], state: "ae2:interface", nbt: {fuzzy_mode: "IGNORE_ALL", id: "ae2:interface", priority: 0, proxy: {g: 25566L, k: -1L, p: 1}, visual: {}}},
        {pos: [0, 0, 2], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 1b, crafting_scheduling_mode: "ANY", id: "ae2:crafting_unit", inventory: [], proxy: {g: 25566L, k: -1L, p: 1}, visual: {}}},
        {pos: [0, 0, 4], state: "ae2:drive{facing:south,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item1: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item2: {}, item3: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item4: {}, item5: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item6: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 25566L, k: -1L, p: 1}, visual: {cell0: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell1: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell3: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell5: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell6: {id: "ae2:item_storage_cell_256k", state: "empty"}, online: 1b}}},
        {pos: [1, 0, 0], state: "ae2:interface", nbt: {fuzzy_mode: "IGNORE_ALL", id: "ae2:interface", priority: 0, proxy: {g: 25566L, k: -1L, p: 1}, visual: {}}},
        {pos: [1, 0, 4], state: "ae2:drive{facing:south,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {}, item1: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item2: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item3: {}, item4: {}, item5: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item6: {}, item7: {}, item8: {}, item9: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}}, priority: 0, proxy: {g: 25566L, k: -1L, p: 1}, visual: {cell1: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell2: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell5: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell9: {id: "ae2:item_storage_cell_256k", state: "empty"}, online: 1b}}},
        {pos: [2, 0, 2], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 1b, crafting_scheduling_mode: "ANY", id: "ae2:crafting_unit", inventory: [], proxy: {g: 25566L, k: -1L, p: 1}, visual: {}}},
        {pos: [3, 0, 0], state: "ae2:creative_energy_cell", nbt: {id: "ae2:creative_energy_cell", proxy: {g: 25566L, k: -1L, p: 1}, visual: {}}},
        {pos: [3, 0, 3], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 25566L, k: -1L, p: 1}, visual: {}}},
        {pos: [3, 0, 4], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 25566L, k: -1L, p: 1}, returnInv: [], sendList: [], visual: {}}},
        {pos: [4, 0, 0], state: "ae2:drive{facing:north,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item1: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 25566L, k: -1L, p: 0}, visual: {cell0: {id: "ae2:item_storage_cell_1k", state: "empty"}, online: 1b}}},
        {pos: [4, 0, 3], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 25566L, k: -1L, p: 1}, returnInv: [], sendList: [], visual: {}}},
        {pos: [4, 0, 4], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 25566L, k: -1L, p: 1}, visual: {}}},
        {pos: [0, 1, 0], state: "ae2:interface", nbt: {fuzzy_mode: "IGNORE_ALL", id: "ae2:interface", priority: 0, proxy: {g: 25566L, k: -1L, p: 1}, visual: {}}},
        {pos: [0, 1, 2], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_unit", proxy: {g: 25566L, k: -1L, p: 1}, visual: {}}},
        {pos: [0, 1, 4], state: "ae2:drive{facing:south,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item1: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item2: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item3: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item4: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item5: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item6: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 25566L, k: -1L, p: 1}, visual: {cell0: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell1: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell2: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell3: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell4: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell5: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell6: {id: "ae2:item_storage_cell_1k", state: "empty"}, online: 1b}}},
        {pos: [1, 1, 0], state: "ae2:interface", nbt: {fuzzy_mode: "IGNORE_ALL", id: "ae2:interface", priority: 0, proxy: {g: 25566L, k: -1L, p: 1}, visual: {}}},
        {pos: [1, 1, 4], state: "ae2:drive{facing:south,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item1: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item2: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item3: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item4: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item5: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item6: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item7: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item8: {}, item9: {}}, priority: 0, proxy: {g: 25566L, k: -1L, p: 1}, visual: {cell0: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell1: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell2: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell3: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell4: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell5: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell6: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell7: {id: "ae2:item_storage_cell_1k", state: "empty"}, online: 1b}}},
        {pos: [2, 1, 2], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_unit", proxy: {g: 25566L, k: -1L, p: 1}, visual: {}}},
        {pos: [3, 1, 3], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 25566L, k: -1L, p: 1}, returnInv: [], sendList: [], visual: {}}},
        {pos: [3, 1, 4], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 25566L, k: -1L, p: 1}, visual: {}}},
        {pos: [4, 1, 0], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 25566L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsDown: 1, channelsUp: 2, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {filter_type: "ALL", gn: {g: 25566L, k: -1L, p: 1}, id: "ae2:crafting_terminal", sort_by: "NAME", sort_direction: "ASCENDING", spin: 0b, view_mode: "ALL", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [4, 1, 3], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 25566L, k: -1L, p: 1}, visual: {}}},
        {pos: [4, 1, 4], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 25566L, k: -1L, p: 1}, returnInv: [], sendList: [], visual: {}}},
        {pos: [7, 1, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 1, channelsUp: 1, connections: ["up", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsSouth: 1, channelsUp: 1, connections: ["up", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsNorth: 1, connections: ["north"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {freq: 30100s, gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [8, 1, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:red_smart_cable", visual: {channelsEast: 1, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {freq: 13572s, gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [8, 1, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsWest: 1, connections: ["west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {freq: -1838s, gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [9, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:red_smart_cable", visual: {channelsSouth: 1, connections: ["south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {freq: -27659s, gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [9, 1, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:red_smart_cable", visual: {channelsEast: 1, channelsNorth: 1, channelsSouth: 1, channelsUp: 4, channelsWest: 1, connections: ["up", "north", "south", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 1, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:red_smart_cable", visual: {channelsNorth: 1, connections: ["north"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {freq: 30372s, gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [10, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 1, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {freq: 10577s, gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [10, 1, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:red_smart_cable", visual: {channelsWest: 1, connections: ["west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {freq: 8496s, gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [10, 1, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsSouth: 1, connections: ["south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {freq: -9263s, gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [10, 1, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsNorth: 1, channelsUp: 1, connections: ["up", "north"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [11, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsUp: 1, channelsWest: 1, connections: ["up", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 2, 0], state: "ae2:interface", nbt: {fuzzy_mode: "IGNORE_ALL", id: "ae2:interface", priority: 0, proxy: {g: 25566L, k: -1L, p: 1}, visual: {}}},
        {pos: [0, 2, 2], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_unit", proxy: {g: 25566L, k: -1L, p: 1}, visual: {}}},
        {pos: [1, 2, 0], state: "ae2:interface", nbt: {fuzzy_mode: "IGNORE_ALL", id: "ae2:interface", priority: 0, proxy: {g: 25566L, k: -1L, p: 1}, visual: {}}},
        {pos: [1, 2, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25566L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsDown: 4, channelsUp: 4, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 2, 2], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_unit", proxy: {g: 25566L, k: -1L, p: 1}, visual: {}}},
        {pos: [3, 2, 3], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 25566L, k: -1L, p: 1}, visual: {}}},
        {pos: [3, 2, 4], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 25566L, k: -1L, p: 1}, returnInv: [], sendList: [], visual: {}}},
        {pos: [4, 2, 0], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 25566L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsDown: 2, channelsUp: 3, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {filter_type: "ALL", gn: {g: 25566L, k: -1L, p: 1}, id: "ae2:pattern_encoding_terminal", mode: "CRAFTING", sort_by: "NAME", sort_direction: "ASCENDING", spin: 0b, substitute: 0b, substituteFluids: 1b, view_mode: "ALL", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [4, 2, 3], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 25566L, k: -1L, p: 1}, returnInv: [], sendList: [], visual: {}}},
        {pos: [4, 2, 4], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 25566L, k: -1L, p: 1}, visual: {}}},
        {pos: [7, 2, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsSouth: 1, connections: ["south"], missingChannel: 0b, powered: 1b}}, east: {freq: -14738s, gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 2, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsNorth: 1, channelsSouth: 2, channelsUp: 4, connections: ["up", "north", "south"], missingChannel: 0b, powered: 1b}}, east: {freq: -28970s, gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 2, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 1, channelsNorth: 2, connections: ["down", "north"], missingChannel: 0b, powered: 1b}}, east: {freq: 19726s, gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 1, channelsEast: 2, connections: ["down", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", south: {freq: 24820s, gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [8, 2, 1], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25566L, k: -1L, p: 0}, visual: {}}},
        {pos: [8, 2, 2], state: "ae2:controller{state:online,type:column_z}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25566L, k: -1L, p: 0}, visual: {}}},
        {pos: [8, 2, 3], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25566L, k: -1L, p: 0}, visual: {}}},
        {pos: [8, 2, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 1, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {freq: 11699s, gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [9, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 1, channelsUp: 4, channelsWest: 2, connections: ["up", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", south: {freq: -18394s, gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [9, 2, 1], state: "ae2:controller{state:online,type:column_x}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25566L, k: -1L, p: 0}, visual: {}}},
        {pos: [9, 2, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:red_smart_cable", visual: {channelsDown: 4, channelsUp: 8, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, east: {freq: 0s, gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {freq: 0s, gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, south: {freq: 0s, gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {freq: 0s, gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [9, 2, 3], state: "ae2:controller{state:online,type:column_x}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25566L, k: -1L, p: 0}, visual: {}}},
        {pos: [9, 2, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 2, channelsUp: 4, channelsWest: 1, connections: ["up", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {freq: 29627s, gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [10, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsWest: 1, connections: ["west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", south: {freq: 12072s, gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [10, 2, 1], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25566L, k: -1L, p: 0}, visual: {}}},
        {pos: [10, 2, 2], state: "ae2:controller{state:online,type:column_z}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25566L, k: -1L, p: 0}, visual: {}}},
        {pos: [10, 2, 3], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25566L, k: -1L, p: 0}, visual: {}}},
        {pos: [10, 2, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 1, channelsWest: 2, connections: ["down", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {freq: -2321s, gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [11, 2, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 1, channelsSouth: 2, connections: ["down", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {freq: 12920s, gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [11, 2, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsNorth: 2, channelsSouth: 1, channelsUp: 4, connections: ["up", "north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {freq: 2339s, gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [11, 2, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsNorth: 1, connections: ["north"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {freq: -16277s, gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [0, 3, 2], state: "ae2:4k_crafting_storage{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_storage", proxy: {g: 25566L, k: -1L, p: 1}, visual: {}}},
        {pos: [1, 3, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25566L, k: -1L, p: 1}, id: "ae2:blue_smart_dense_cable", visual: {channelsDown: 6, channelsEast: 6, connections: ["down", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 3, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25566L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsDown: 4, channelsUp: 4, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 3, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25566L, k: -1L, p: 1}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 6, channelsWest: 6, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 3, 2], state: "ae2:4k_crafting_storage{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_storage", proxy: {g: 25566L, k: -1L, p: 1}, visual: {}}},
        {pos: [3, 3, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25566L, k: -1L, p: 1}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 6, channelsUp: 0, channelsWest: 6, connections: ["up", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 3, 3], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 25566L, k: -1L, p: 1}, returnInv: [], sendList: [], visual: {}}},
        {pos: [3, 3, 4], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 25566L, k: -1L, p: 1}, visual: {}}},
        {pos: [4, 3, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25566L, k: -1L, p: 1}, id: "ae2:blue_smart_dense_cable", visual: {channelsDown: 3, channelsUp: 9, channelsWest: 6, connections: ["down", "up", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 3, 3], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 25566L, k: -1L, p: 1}, visual: {}}},
        {pos: [4, 3, 4], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 25566L, k: -1L, p: 1}, returnInv: [], sendList: [], visual: {}}},
        {pos: [7, 3, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 4, channelsUp: 4, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 3, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:red_smart_cable", visual: {channelsEast: 1, channelsSouth: 0, connections: ["south", "east"], missingChannel: 0b, powered: 1b}}, down: {freq: -4455s, gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 3, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:red_smart_cable", visual: {channelsEast: 2, channelsNorth: 0, channelsSouth: 1, connections: ["north", "south", "east"], missingChannel: 0b, powered: 1b}}, down: {freq: 18831s, gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 3, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:red_smart_cable", visual: {channelsEast: 0, channelsNorth: 1, connections: ["north", "east"], missingChannel: 0b, powered: 1b}}, down: {freq: 3030s, gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 3, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 4, channelsUp: 4, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 3, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:red_smart_cable", visual: {channelsEast: 1, channelsSouth: 3, channelsWest: 1, connections: ["south", "west", "east"], missingChannel: 0b, powered: 1b}}, down: {freq: 20341s, gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 3, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:fluix_smart_dense_cable", visual: {channelsDown: 8, channelsEast: 1, channelsNorth: 3, channelsSouth: 2, channelsUp: 16, channelsWest: 2, connections: ["down", "up", "north", "south", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 3, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:red_smart_cable", visual: {channelsEast: 1, channelsNorth: 2, channelsWest: 0, connections: ["north", "west", "east"], missingChannel: 0b, powered: 1b}}, down: {freq: -15289s, gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 3, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 4, channelsUp: 4, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 3, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:red_smart_cable", visual: {channelsSouth: 0, channelsWest: 1, connections: ["south", "west"], missingChannel: 0b, powered: 1b}}, down: {freq: 13080s, gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 3, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:red_smart_cable", visual: {channelsNorth: 0, channelsSouth: 0, channelsWest: 1, connections: ["north", "south", "west"], missingChannel: 0b, powered: 1b}}, down: {freq: -27101s, gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 3, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:red_smart_cable", visual: {channelsNorth: 0, channelsWest: 1, connections: ["north", "west"], missingChannel: 0b, powered: 1b}}, down: {freq: 11678s, gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [11, 3, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 4, channelsUp: 4, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 4, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25566L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsDown: 1, channelsUp: 1, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 4, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25566L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsDown: 4, channelsUp: 4, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 4, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25566L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsDown: 1, channelsUp: 1, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 4, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 1}, id: "ae2:orange_smart_cable", visual: {channelsEast: 0, connections: ["east"], missingChannel: 0b, powered: 1b}}, down: {gn: {g: 25113L, k: -1L, p: 1}, id: "ae2:quartz_fiber", outer: {g: 25566L, k: -1L, p: 1}, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 4, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 1}, id: "ae2:orange_smart_cable", visual: {channelsUp: 1, channelsWest: 0, connections: ["up", "west"], missingChannel: 0b, powered: 1b}}, down: {freq: -14738s, gn: {g: 25113L, k: -1L, p: 1}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 1}, output: 1b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 4, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25566L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsDown: 8, channelsUp: 8, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 4, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 4, channelsEast: 4, connections: ["down", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 4, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 4, channelsWest: 4, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 4, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 4, channelsSouth: 4, connections: ["down", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 4, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsNorth: 4, channelsSouth: 4, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 4, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 72689L, k: -1L, p: 0}, id: "ae2:fluix_smart_dense_cable", visual: {channelsDown: 16, channelsEast: 4, channelsNorth: 4, channelsSouth: 4, channelsUp: 32, channelsWest: 4, connections: ["down", "up", "north", "south", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 4, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsNorth: 4, channelsSouth: 4, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 4, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 4, channelsNorth: 4, connections: ["down", "north"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 4, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 4, channelsWest: 4, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [11, 4, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 4, channelsWest: 4, connections: ["down", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 5, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25566L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsDown: 1, channelsEast: 1, connections: ["down", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 5, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25566L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsEast: 1, channelsSouth: 2, channelsWest: 1, connections: ["south", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 5, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25566L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 2, channelsSouth: 2, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 5, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25566L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsDown: 4, channelsEast: 6, channelsNorth: 2, connections: ["down", "north", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 5, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25566L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsDown: 1, channelsWest: 1, connections: ["down", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 5, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25566L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsEast: 6, channelsWest: 6, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 5, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25566L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsEast: 6, channelsWest: 6, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 5, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 1}, id: "ae2:orange_smart_cable", visual: {channelsDown: 1, channelsUp: 1, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 5, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25566L, k: -1L, p: 1}, id: "ae2:blue_smart_dense_cable", visual: {channelsDown: 8, channelsUp: 14, channelsWest: 6, connections: ["down", "up", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 5, 2], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25113L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 6, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 1}, id: "ae2:orange_smart_cable", visual: {channelsDown: 1, channelsSouth: 1, connections: ["down", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 6, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 1}, id: "ae2:orange_smart_cable", visual: {channelsNorth: 1, channelsSouth: 1, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 6, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:orange_smart_dense_cable", visual: {channelsEast: 2, channelsNorth: 1, channelsSouth: 1, connections: ["north", "south", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 6, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 1}, id: "ae2:orange_smart_cable", visual: {channelsNorth: 1, channelsSouth: 1, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 6, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 1}, id: "ae2:orange_smart_cable", visual: {channelsNorth: 1, connections: ["north"], missingChannel: 0b, powered: 1b}}, down: {freq: 20341s, gn: {g: 25113L, k: -1L, p: 1}, id: "ae2:me_p2p_tunnel", outer: {g: 25566L, k: -1L, p: 1}, output: 1b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 6, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:orange_smart_dense_cable", visual: {channelsEast: 2, channelsWest: 2, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 6, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:orange_smart_dense_cable", visual: {channelsEast: 2, channelsWest: 2, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 6, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:orange_smart_dense_cable", visual: {channelsEast: 2, channelsWest: 2, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 6, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:orange_smart_dense_cable", visual: {channelsEast: 2, channelsWest: 2, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 6, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25113L, k: -1L, p: 0}, id: "ae2:orange_smart_dense_cable", visual: {channelsDown: 2, channelsWest: 2, connections: ["down", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}}
    ],
    entities: [],
    palette: [
        "ae2:interface",
        "ae2:crafting_accelerator{formed:true,powered:true}",
        "ae2:drive{facing:south,spin:0}",
        "ae2:creative_energy_cell",
        "ae2:molecular_assembler{powered:true}",
        "ae2:pattern_provider{push_direction:all}",
        "ae2:drive{facing:north,spin:0}",
        "ae2:cable_bus{light_level:9,waterlogged:false}",
        "ae2:cable_bus{light_level:0,waterlogged:false}",
        "ae2:controller{state:online,type:block}",
        "ae2:controller{state:online,type:column_z}",
        "ae2:controller{state:online,type:column_x}",
        "ae2:4k_crafting_storage{formed:true,powered:true}"
    ]
}