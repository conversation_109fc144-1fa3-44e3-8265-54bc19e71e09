{
    DataVersion: 3337,
    size: [8, 5, 3],
    data: [
        {pos: [0, 0, 0], state: "ae2:charger{facing:north,spin:0}", nbt: {id: "ae2:charger", internalCurrentPower: 1600.0d, inv: {item0: {}}, proxy: {g: 25503L, k: -1L, p: 0}, visual: {}}},
        {pos: [1, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 465L, k: -1L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsUp: 2, connections: ["up"], missingChannel: 0b, powered: 1b}}, east: {id: "ae2:cable_anchor", visual: {}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 465L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [2, 0, 0], state: "ae2:drive{facing:north,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item1: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 399L, k: -1L, p: 0}, visual: {cell0: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell1: {id: "ae2:item_storage_cell_1k", state: "empty"}, online: 1b}}},
        {pos: [3, 0, 0], state: "minecraft:barrel{facing:north,open:false}", nbt: {Items: [], id: "minecraft:barrel"}},
        {pos: [3, 0, 1], state: "ae2:growth_accelerator{facing:up,powered:true}", nbt: {id: "ae2:growth_accelerator", proxy: {g: 25503L, k: -1L, p: 1}, visual: {}}},
        {pos: [4, 0, 0], state: "ae2:quartz_cluster{facing:north,waterlogged:false}"},
        {pos: [4, 0, 1], state: "ae2:flawless_budding_quartz"},
        {pos: [5, 0, 0], state: "ae2:sky_stone_tank", nbt: {amount: 0L, id: "ae2:sky_tank", variant: {fluid: "minecraft:empty"}, visual: {}}},
        {pos: [5, 0, 1], state: "ae2:growth_accelerator{facing:up,powered:true}", nbt: {id: "ae2:growth_accelerator", proxy: {g: 25503L, k: -1L, p: 1}, visual: {}}},
        {pos: [6, 0, 0], state: "minecraft:barrel{facing:north,open:false}", nbt: {Items: [], id: "minecraft:barrel"}},
        {pos: [0, 1, 0], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 25503L, k: -1L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [0, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25503L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 1, channelsUp: 1, connections: ["up", "north"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 465L, k: -1L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsDown: 2, channelsUp: 2, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 465L, k: -1L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [2, 1, 0], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 399L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 3, channelsUp: 3, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {filter_type: "ALL", gn: {g: 399L, k: -1L, p: 0}, id: "ae2:crafting_terminal", sort_by: "NAME", sort_direction: "ASCENDING", spin: 0b, view_mode: "ALL", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [3, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 162L, k: -1L, p: 0}, id: "ae2:purple_smart_cable", visual: {channelsUp: 2, connections: ["up"], missingChannel: 0b, powered: 1b}}, down: {fuzzy_mode: "IGNORE_ALL", gn: {g: 162L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25503L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 0, channelsEast: 0, connections: ["down", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {id: "ae2:cable_anchor", visual: {}}, visual: {}}},
        {pos: [4, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 184L, k: -1L, p: 0}, id: "ae2:red_smart_cable", visual: {channelsUp: 2, connections: ["up"], missingChannel: 0b, powered: 1b}}, down: {gn: {g: 184L, k: -1L, p: 0}, id: "ae2:annihilation_plane", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25503L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 0, channelsUp: 0, channelsWest: 0, connections: ["up", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 445L, k: -1L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsUp: 2, connections: ["up"], missingChannel: 0b, powered: 1b}}, down: {fuzzy_mode: "IGNORE_ALL", gn: {g: 445L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25503L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 0, channelsWest: 0, connections: ["down", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {id: "ae2:cable_anchor", visual: {}}, visual: {}}},
        {pos: [6, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 215L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsUp: 2, connections: ["up"], missingChannel: 0b, powered: 1b}}, down: {fuzzy_mode: "IGNORE_ALL", gn: {g: 215L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 1, 1], state: "ae2:drive{facing:north,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item1: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item2: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item3: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 25503L, k: -1L, p: 0}, visual: {cell0: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell1: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell2: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell3: {id: "ae2:item_storage_cell_1k", state: "empty"}, online: 1b}}},
        {pos: [0, 2, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25503L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsDown: 1, channelsEast: 1, connections: ["down", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25503L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsSouth: 0, connections: ["south"], missingChannel: 0b, powered: 1b}}, down: {gn: {g: 25503L, k: -1L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 465L, k: -1L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 2, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25503L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 1, channelsNorth: 0, channelsWest: 1, connections: ["north", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 399L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 3, channelsUp: 3, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", south: {gn: {g: 399L, k: -1L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 25503L, k: -1L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [2, 2, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25503L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 2, channelsNorth: 0, channelsUp: 1, channelsWest: 1, connections: ["up", "north", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 162L, k: -1L, p: 0}, id: "ae2:purple_smart_cable", visual: {channelsDown: 2, channelsUp: 2, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", south: {gn: {g: 162L, k: -1L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 25503L, k: -1L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [3, 2, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25503L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 2, channelsNorth: 0, channelsWest: 2, connections: ["north", "west", "east"], missingChannel: 0b, powered: 1b}}, down: {id: "ae2:cable_anchor", visual: {}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 184L, k: -1L, p: 0}, id: "ae2:red_smart_cable", visual: {channelsDown: 2, channelsUp: 2, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", south: {gn: {g: 184L, k: -1L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 25503L, k: -1L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [4, 2, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25503L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsDown: 0, channelsEast: 2, channelsNorth: 0, channelsWest: 2, connections: ["down", "north", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 445L, k: -1L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsDown: 2, channelsUp: 2, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", south: {gn: {g: 445L, k: -1L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 25503L, k: -1L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [5, 2, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25503L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 2, channelsNorth: 0, channelsWest: 2, connections: ["north", "west", "east"], missingChannel: 0b, powered: 1b}}, down: {id: "ae2:cable_anchor", visual: {}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 215L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 2, channelsUp: 2, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", south: {gn: {g: 215L, k: -1L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 25503L, k: -1L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [6, 2, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25503L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 2, channelsNorth: 0, channelsWest: 2, connections: ["north", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 2, 1], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25503L, k: -1L, p: 0}, visual: {}}},
        {pos: [2, 3, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 399L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 3, connections: ["down"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 399L, k: -1L, p: 0}, id: "ae2:cable_interface", priority: 0, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [2, 3, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25503L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 1, connections: ["down"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 25503L, k: -1L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [3, 3, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 162L, k: -1L, p: 0}, id: "ae2:purple_smart_cable", visual: {channelsDown: 2, connections: ["down"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {fuzzy_mode: "IGNORE_ALL", gn: {g: 162L, k: -1L, p: 0}, id: "ae2:formation_plane", place_block: "YES", priority: 0, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [4, 3, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 184L, k: -1L, p: 0}, id: "ae2:red_smart_cable", visual: {channelsDown: 2, connections: ["down"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {access: "READ_WRITE", config: [{"#": 0L, "#c": "ae2:i", id: "ae2:certus_quartz_crystal"}], filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 184L, k: -1L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [5, 3, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 445L, k: -1L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsDown: 2, connections: ["down"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 445L, k: -1L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [6, 3, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 215L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 2, connections: ["down"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 215L, k: -1L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [7, 3, 1], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 25503L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 1, connections: ["down"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {filter_type: "ALL", gn: {g: 25503L, k: -1L, p: 0}, id: "ae2:crafting_terminal", sort_by: "NAME", sort_direction: "ASCENDING", spin: 0b, view_mode: "ALL", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [3, 4, 0], state: "minecraft:chipped_anvil{facing:west}"},
        {pos: [4, 4, 0], state: "minecraft:barrel{facing:north,open:false}", nbt: {Items: [{Count: 1b, Slot: 0b, id: "ae2:certus_quartz_dust"}, {Count: 64b, Slot: 1b, id: "ae2:certus_quartz_crystal"}, {Count: 64b, Slot: 2b, id: "ae2:certus_quartz_crystal"}, {Count: 64b, Slot: 3b, id: "ae2:certus_quartz_crystal"}, {Count: 64b, Slot: 4b, id: "ae2:certus_quartz_crystal"}, {Count: 64b, Slot: 5b, id: "ae2:certus_quartz_crystal"}, {Count: 64b, Slot: 6b, id: "ae2:certus_quartz_crystal"}, {Count: 64b, Slot: 7b, id: "ae2:certus_quartz_crystal"}, {Count: 64b, Slot: 8b, id: "ae2:certus_quartz_crystal"}, {Count: 64b, Slot: 9b, id: "ae2:certus_quartz_crystal"}, {Count: 64b, Slot: 10b, id: "ae2:certus_quartz_crystal"}, {Count: 64b, Slot: 11b, id: "ae2:certus_quartz_crystal"}, {Count: 64b, Slot: 12b, id: "ae2:certus_quartz_crystal"}, {Count: 64b, Slot: 13b, id: "ae2:certus_quartz_crystal"}, {Count: 64b, Slot: 14b, id: "ae2:certus_quartz_crystal"}, {Count: 64b, Slot: 15b, id: "ae2:certus_quartz_crystal"}, {Count: 64b, Slot: 16b, id: "ae2:certus_quartz_crystal"}, {Count: 64b, Slot: 17b, id: "ae2:certus_quartz_crystal"}, {Count: 64b, Slot: 18b, id: "ae2:certus_quartz_crystal"}, {Count: 64b, Slot: 19b, id: "ae2:certus_quartz_crystal"}, {Count: 64b, Slot: 20b, id: "ae2:certus_quartz_crystal"}, {Count: 64b, Slot: 21b, id: "ae2:certus_quartz_crystal"}, {Count: 64b, Slot: 22b, id: "ae2:certus_quartz_crystal"}, {Count: 64b, Slot: 23b, id: "ae2:certus_quartz_crystal"}, {Count: 64b, Slot: 24b, id: "ae2:certus_quartz_crystal"}, {Count: 64b, Slot: 25b, id: "ae2:certus_quartz_crystal"}, {Count: 64b, Slot: 26b, id: "ae2:certus_quartz_crystal"}], id: "minecraft:barrel"}},
        {pos: [5, 4, 0], state: "ae2:sky_stone_tank", nbt: {amount: 0L, id: "ae2:sky_tank", variant: {fluid: "minecraft:empty"}, visual: {}}},
        {pos: [6, 4, 0], state: "minecraft:barrel{facing:north,open:false}", nbt: {Items: [], id: "minecraft:barrel"}}
    ],
    entities: [],
    palette: [
        "ae2:flawless_budding_quartz",
        "ae2:quartz_cluster{facing:north,waterlogged:false}",
        "minecraft:chipped_anvil{facing:west}",
        "ae2:charger{facing:north,spin:0}",
        "ae2:cable_bus{light_level:0,waterlogged:false}",
        "ae2:drive{facing:north,spin:0}",
        "minecraft:barrel{facing:north,open:false}",
        "ae2:growth_accelerator{facing:up,powered:true}",
        "ae2:creative_energy_cell",
        "ae2:sky_stone_tank",
        "ae2:pattern_provider{push_direction:all}",
        "ae2:cable_bus{light_level:9,waterlogged:false}",
        "ae2:controller{state:online,type:block}"
    ]
}