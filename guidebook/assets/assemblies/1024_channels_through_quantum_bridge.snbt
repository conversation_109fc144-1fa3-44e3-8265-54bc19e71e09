{
    DataVersion: 3337,
    size: [11, 6, 6],
    data: [
        {pos: [0, 0, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsUp: 4, connections: ["up"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 1, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [2, 0, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsUp: 4, connections: ["up"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 1, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [3, 0, 1], state: "ae2:quantum_ring{formed:true,waterlogged:false}", nbt: {id: "ae2:quantum_ring", inv: {item0: {}}, proxy: {g: 25037L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 0, 1], state: "ae2:quantum_ring{formed:true,waterlogged:false}", nbt: {id: "ae2:quantum_ring", inv: {item0: {}}, proxy: {g: 25037L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 0, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 0, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 0, 1], state: "ae2:quantum_ring{formed:true,waterlogged:false}", nbt: {id: "ae2:quantum_ring", inv: {item0: {}}, proxy: {g: 25037L, k: -1L, p: 0}, visual: {}}},
        {pos: [5, 0, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsUp: 1, connections: ["up"], missingChannel: 0b, powered: 1b}}, east: {id: "ae2:cable_anchor", visual: {}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {freq: 6946s, gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 73159L, k: -1L, p: 0}, output: 1b, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [6, 0, 5], state: "ae2:energy_cell{fullness:0}", nbt: {id: "ae2:energy_cell", internalCurrentPower: 0.0d, proxy: {g: 25037L, k: -1L, p: 1}, visual: {}}},
        {pos: [8, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsUp: 1, connections: ["up"], missingChannel: 0b, powered: 1b}}, east: {freq: 6946s, gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 73159L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 0, 5], state: "ae2:quantum_ring{formed:true,waterlogged:false}", nbt: {id: "ae2:quantum_ring", inv: {item0: {}}, proxy: {g: 25037L, k: -1L, p: 0}, visual: {}}},
        {pos: [9, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 0, channelsWest: 0, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 0, 5], state: "ae2:quantum_ring{formed:true,waterlogged:false}", nbt: {id: "ae2:quantum_ring", inv: {item0: {}}, proxy: {g: 25037L, k: -1L, p: 0}, visual: {}}},
        {pos: [10, 0, 1], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 73159L, k: -1L, p: 0}, visual: {}}},
        {pos: [10, 0, 5], state: "ae2:quantum_ring{formed:true,waterlogged:false}", nbt: {id: "ae2:quantum_ring", inv: {item0: {}}, proxy: {g: 25037L, k: -1L, p: 0}, visual: {}}},
        {pos: [0, 1, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 4, channelsUp: 8, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 1, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [2, 1, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 4, channelsUp: 8, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 1, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [3, 1, 1], state: "ae2:quantum_ring{formed:true,waterlogged:false}", nbt: {id: "ae2:quantum_ring", inv: {item0: {}}, proxy: {g: 25037L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 1, 1], state: "ae2:quantum_link{formed:true,waterlogged:false}", nbt: {id: "ae2:quantum_ring", inv: {item0: {Count: 1b, id: "ae2:quantum_entangled_singularity", tag: {freq: 167842459780200L}}}, proxy: {g: 25037L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 1, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:white_smart_dense_cable", visual: {channelsEast: 0, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 1, 1], state: "ae2:quantum_ring{formed:true,waterlogged:false}", nbt: {id: "ae2:quantum_ring", inv: {item0: {}}, proxy: {g: 25037L, k: -1L, p: 0}, visual: {}}},
        {pos: [5, 1, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 1, channelsEast: 6, channelsUp: 4, connections: ["down", "up", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {freq: 22056s, gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 73159L, k: -1L, p: 0}, output: 1b, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [6, 1, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:orange_smart_dense_cable", visual: {channelsDown: 0, channelsEast: 6, channelsWest: 6, connections: ["down", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 1, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:orange_smart_dense_cable", visual: {channelsEast: 6, channelsWest: 6, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 1, channelsUp: 2, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, east: {freq: 22056s, gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 73159L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 1, 5], state: "ae2:quantum_ring{formed:true,waterlogged:false}", nbt: {id: "ae2:quantum_ring", inv: {item0: {}}, proxy: {g: 25037L, k: -1L, p: 0}, visual: {}}},
        {pos: [9, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:white_smart_dense_cable", visual: {channelsEast: 0, channelsWest: 0, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 1, 5], state: "ae2:quantum_link{formed:true,waterlogged:false}", nbt: {id: "ae2:quantum_ring", inv: {item0: {Count: 1b, id: "ae2:quantum_entangled_singularity", tag: {freq: 167842459780200L}}}, proxy: {g: 25037L, k: -1L, p: 0}, visual: {}}},
        {pos: [10, 1, 1], state: "ae2:controller{state:online,type:column_y}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 73159L, k: -1L, p: 0}, visual: {}}},
        {pos: [10, 1, 5], state: "ae2:quantum_ring{formed:true,waterlogged:false}", nbt: {id: "ae2:quantum_ring", inv: {item0: {}}, proxy: {g: 25037L, k: -1L, p: 0}, visual: {}}},
        {pos: [0, 2, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsDown: 8, channelsEast: 16, channelsUp: 8, connections: ["down", "up", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 2, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 16, channelsWest: 16, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 2, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsDown: 8, channelsEast: 32, channelsUp: 8, channelsWest: 16, connections: ["down", "up", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 2, 1], state: "ae2:quantum_ring{formed:true,waterlogged:false}", nbt: {id: "ae2:quantum_ring", inv: {item0: {}}, proxy: {g: 25037L, k: -1L, p: 0}, visual: {}}},
        {pos: [3, 2, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 32, channelsWest: 32, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 2, 1], state: "ae2:quantum_ring{formed:true,waterlogged:false}", nbt: {id: "ae2:quantum_ring", inv: {item0: {}}, proxy: {g: 25037L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 2, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 32, channelsWest: 32, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 2, 1], state: "ae2:quantum_ring{formed:true,waterlogged:false}", nbt: {id: "ae2:quantum_ring", inv: {item0: {}}, proxy: {g: 25037L, k: -1L, p: 0}, visual: {}}},
        {pos: [5, 2, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 4, channelsUp: 3, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {freq: -11195s, gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 73159L, k: -1L, p: 0}, output: 1b, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [8, 2, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 2, channelsUp: 3, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, east: {freq: -11195s, gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 73159L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 2, 5], state: "ae2:quantum_ring{formed:true,waterlogged:false}", nbt: {id: "ae2:quantum_ring", inv: {item0: {}}, proxy: {g: 25037L, k: -1L, p: 0}, visual: {}}},
        {pos: [9, 2, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 32, channelsWest: 32, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 2, 5], state: "ae2:quantum_ring{formed:true,waterlogged:false}", nbt: {id: "ae2:quantum_ring", inv: {item0: {}}, proxy: {g: 25037L, k: -1L, p: 0}, visual: {}}},
        {pos: [10, 2, 1], state: "ae2:controller{state:online,type:column_y}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 73159L, k: -1L, p: 0}, visual: {}}},
        {pos: [10, 2, 5], state: "ae2:quantum_ring{formed:true,waterlogged:false}", nbt: {id: "ae2:quantum_ring", inv: {item0: {}}, proxy: {g: 25037L, k: -1L, p: 0}, visual: {}}},
        {pos: [0, 3, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 8, channelsUp: 4, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 1, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [2, 3, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 8, channelsUp: 4, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 1, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [4, 3, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:orange_smart_dense_cable", visual: {channelsDown: 6, channelsUp: 6, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 3, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:white_smart_dense_cable", visual: {channelsEast: 0, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 3, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 3, channelsUp: 2, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {freq: 4000s, gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 73159L, k: -1L, p: 0}, output: 1b, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [8, 3, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 3, channelsUp: 4, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, east: {freq: 4000s, gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 73159L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 3, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:white_smart_dense_cable", visual: {channelsEast: 0, channelsWest: 0, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 3, 1], state: "ae2:controller{state:online,type:column_y}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 73159L, k: -1L, p: 0}, visual: {}}},
        {pos: [0, 4, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 4, connections: ["down"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 1, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [2, 4, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 4, connections: ["down"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 1, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [3, 4, 1], state: "ae2:energy_cell{fullness:0}", nbt: {id: "ae2:energy_cell", internalCurrentPower: 0.0d, proxy: {g: 25037L, k: -1L, p: 1}, visual: {}}},
        {pos: [4, 4, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:orange_smart_dense_cable", visual: {channelsDown: 6, channelsEast: 6, channelsWest: 0, connections: ["down", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 4, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 0, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 4, 1], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25037L, k: -1L, p: 0}, visual: {}}},
        {pos: [5, 4, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 2, channelsUp: 1, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {freq: 9363s, gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 73159L, k: -1L, p: 0}, output: 1b, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [6, 4, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:orange_smart_dense_cable", visual: {channelsEast: 6, channelsWest: 6, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 4, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:orange_smart_dense_cable", visual: {channelsEast: 6, channelsWest: 6, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 4, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 4, channelsUp: 1, channelsWest: 6, connections: ["down", "up", "west"], missingChannel: 0b, powered: 1b}}, east: {freq: 9363s, gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 73159L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 4, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 0, channelsWest: 0, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 4, 1], state: "ae2:controller{state:online,type:column_y}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 73159L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 5, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:white_smart_dense_cable", visual: {channelsEast: 0, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 5, 1], state: "ae2:creative_energy_cell", nbt: {id: "ae2:creative_energy_cell", proxy: {g: 25037L, k: -1L, p: 1}, visual: {}}},
        {pos: [5, 5, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 1, connections: ["down"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {freq: 15413s, gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 73159L, k: -1L, p: 0}, output: 1b, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [8, 5, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsSouth: 0, connections: ["south"], missingChannel: 0b, powered: 1b}}, east: {gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 73159L, k: -1L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 5, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 1, channelsNorth: 0, connections: ["down", "north"], missingChannel: 0b, powered: 1b}}, east: {freq: 15413s, gn: {g: 25037L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 73159L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 5, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:white_smart_cable", visual: {channelsSouth: 0, channelsWest: 0, connections: ["south", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 5, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 73159L, k: -1L, p: 0}, id: "ae2:white_smart_dense_cable", visual: {channelsEast: 0, channelsNorth: 0, channelsWest: 0, connections: ["north", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 5, 1], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 73159L, k: -1L, p: 0}, visual: {}}}
    ],
    entities: [],
    palette: [
        "ae2:cable_bus{light_level:0,waterlogged:false}",
        "ae2:quantum_ring{formed:true,waterlogged:false}",
        "ae2:energy_cell{fullness:0}",
        "ae2:controller{state:online,type:block}",
        "ae2:quantum_link{formed:true,waterlogged:false}",
        "ae2:controller{state:online,type:column_y}",
        "ae2:creative_energy_cell"
    ]
}