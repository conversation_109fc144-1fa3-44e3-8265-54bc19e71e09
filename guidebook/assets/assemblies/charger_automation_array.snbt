{
    DataVersion: 3337,
    size: [4, 3, 3],
    data: [
        {pos: [0, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27359L, k: -1L, p: 2}, id: "ae2:orange_smart_cable", visual: {channelsEast: 5, channelsSouth: 5, channelsUp: 5, connections: ["up", "south", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27359L, k: -1L, p: 2}, id: "ae2:orange_smart_cable", visual: {channelsNorth: 5, channelsUp: 5, connections: ["up", "north"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 27359L, k: -1L, p: 2}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27346L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsEast: 0, channelsSouth: 0, channelsUp: 0, connections: ["up", "south", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {gn: {g: 27346L, k: -1L, p: 2}, id: "ae2:quartz_fiber", outer: {g: 27359L, k: -1L, p: 2}, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [1, 0, 1], state: "ae2:charger{facing:east,spin:0}", nbt: {id: "ae2:charger", internalCurrentPower: 1600.0d, inv: {item0: {}}, proxy: {g: 27346L, k: -1L, p: 2}, visual: {}}},
        {pos: [2, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27346L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsUp: 0, channelsWest: 0, connections: ["up", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27359L, k: -1L, p: 2}, id: "ae2:orange_smart_cable", visual: {channelsDown: 5, channelsSouth: 5, connections: ["down", "south"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 27359L, k: -1L, p: 2}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27359L, k: -1L, p: 2}, id: "ae2:orange_smart_cable", visual: {channelsDown: 5, channelsNorth: 5, channelsSouth: 5, channelsUp: 5, connections: ["down", "up", "north", "south"], missingChannel: 0b, powered: 1b}}, east: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 27359L, k: -1L, p: 2}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 1, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27359L, k: -1L, p: 2}, id: "ae2:orange_smart_cable", visual: {channelsNorth: 5, connections: ["north"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 27359L, k: -1L, p: 2}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 1, 0], state: "ae2:charger{facing:east,spin:1}", nbt: {id: "ae2:charger", internalCurrentPower: 1600.0d, inv: {item0: {}}, proxy: {g: 27346L, k: -1L, p: 2}, visual: {}}},
        {pos: [1, 1, 1], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", patterns: [{Count: 1b, Slot: 0, id: "ae2:processing_pattern", tag: {in: [{"#": 1L, "#c": "ae2:i", id: "ae2:certus_quartz_crystal"}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}], out: [{"#": 1L, "#c": "ae2:i", id: "ae2:charged_certus_quartz_crystal"}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}]}}], priority: 0, proxy: {g: 27346L, k: -1L, p: 2}, returnInv: [], sendList: [], visual: {}}},
        {pos: [1, 1, 2], state: "ae2:charger{facing:east,spin:1}", nbt: {id: "ae2:charger", internalCurrentPower: 1600.0d, inv: {item0: {}}, proxy: {g: 27346L, k: -1L, p: 2}, visual: {}}},
        {pos: [2, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27346L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsDown: 0, channelsSouth: 0, connections: ["down", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27346L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsEast: 1, channelsNorth: 0, channelsWest: 1, connections: ["north", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27346L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsDown: 1, channelsWest: 1, connections: ["west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 2, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27359L, k: -1L, p: 2}, id: "ae2:orange_smart_cable", visual: {channelsDown: 5, connections: ["down"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 27359L, k: -1L, p: 2}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 2, 1], state: "ae2:charger{facing:east,spin:0}", nbt: {id: "ae2:charger", internalCurrentPower: 1600.0d, inv: {item0: {}}, proxy: {g: 27346L, k: -1L, p: 2}, visual: {}}}
    ],
    entities: [],
    palette: [
        "ae2:cable_bus{light_level:0,waterlogged:false}",
        "ae2:charger{facing:east,spin:0}",
        "ae2:controller{state:online,type:block}",
        "ae2:charger{facing:east,spin:1}",
        "ae2:pattern_provider{push_direction:all}"
    ]
}