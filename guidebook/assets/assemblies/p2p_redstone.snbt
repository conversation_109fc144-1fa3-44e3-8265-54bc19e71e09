{
    DataVersion: 3337,
    size: [6, 2, 4],
    data: [
        {pos: [0, 0, 0], state: "minecraft:stone_bricks"},
        {pos: [1, 0, 0], state: "minecraft:stone_bricks"},
        {pos: [1, 0, 3], state: "minecraft:stone_bricks"},
        {pos: [2, 0, 0], state: "minecraft:stone_bricks"},
        {pos: [2, 0, 3], state: "minecraft:stone_bricks"},
        {pos: [3, 0, 0], state: "minecraft:stone_bricks"},
        {pos: [3, 0, 3], state: "minecraft:stone_bricks"},
        {pos: [4, 0, 0], state: "minecraft:stone_bricks"},
        {pos: [4, 0, 3], state: "minecraft:stone_bricks"},
        {pos: [5, 0, 0], state: "minecraft:stone_bricks"},
        {pos: [0, 1, 0], state: "minecraft:redstone_lamp{lit:true}"},
        {pos: [1, 1, 0], state: "minecraft:redstone_wire{east:side,north:none,power:15,south:none,west:side}"},
        {pos: [1, 1, 3], state: "minecraft:redstone_lamp{lit:true}"},
        {pos: [2, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 440L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 2, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {freq: 13753s, gn: {g: 440L, k: -1L, p: 0}, id: "ae2:redstone_p2p_tunnel", output: 1b, power: 15, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [2, 1, 3], state: "minecraft:redstone_wire{east:side,north:none,power:14,south:none,west:side}"},
        {pos: [3, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 440L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 2, channelsWest: 2, connections: ["west"], missingChannel: 0b, powered: 1b}}, east: {freq: 13753s, gn: {g: 440L, k: -1L, p: 0}, id: "ae2:redstone_p2p_tunnel", output: 0b, power: 15, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 1, 3], state: "minecraft:redstone_wire{east:side,north:none,power:15,south:none,west:side}"},
        {pos: [4, 1, 0], state: "minecraft:redstone_wire{east:side,north:none,power:15,south:none,west:side}"},
        {pos: [4, 1, 3], state: "minecraft:lever{face:floor,facing:south,powered:true}"},
        {pos: [5, 1, 0], state: "minecraft:lever{face:floor,facing:south,powered:true}"}
    ],
    entities: [],
    palette: [
        "minecraft:stone_bricks",
        "minecraft:redstone_lamp{lit:true}",
        "minecraft:redstone_wire{east:side,north:none,power:15,south:none,west:side}",
        "minecraft:redstone_wire{east:side,north:none,power:14,south:none,west:side}",
        "minecraft:lever{face:floor,facing:south,powered:true}",
        "ae2:creative_energy_cell",
        "ae2:cable_bus{light_level:0,waterlogged:false}"
    ]
}