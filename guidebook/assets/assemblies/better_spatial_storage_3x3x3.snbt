{
    DataVersion: 3465,
    size: [7, 5, 5],
    data: [
        {pos: [0, 0, 0], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "Z", axisPosition: "START", online: 1b, powered: 1b}}},
        {pos: [0, 0, 1], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "Z", axisPosition: "MIDDLE", online: 1b, powered: 1b}}},
        {pos: [0, 0, 2], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "Z", axisPosition: "MIDDLE", online: 1b, powered: 1b}}},
        {pos: [0, 0, 3], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "Z", axisPosition: "END", online: 1b, powered: 1b}}},
        {pos: [0, 0, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 1106867L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 6, channelsNorth: 1, channelsUp: 5, connections: ["up", "north", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 0, 4], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "X", axisPosition: "START", online: 1b, powered: 1b}}},
        {pos: [2, 0, 0], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "X", axisPosition: "START", online: 1b, powered: 1b}}},
        {pos: [2, 0, 2], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "X", axisPosition: "START", online: 1b, powered: 1b}}},
        {pos: [2, 0, 4], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "X", axisPosition: "MIDDLE", online: 1b, powered: 1b}}},
        {pos: [3, 0, 0], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "X", axisPosition: "MIDDLE", online: 1b, powered: 1b}}},
        {pos: [3, 0, 2], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "X", axisPosition: "MIDDLE", online: 1b, powered: 1b}}},
        {pos: [3, 0, 4], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "X", axisPosition: "MIDDLE", online: 1b, powered: 1b}}},
        {pos: [4, 0, 0], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "X", axisPosition: "END", online: 1b, powered: 1b}}},
        {pos: [4, 0, 2], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "X", axisPosition: "END", online: 1b, powered: 1b}}},
        {pos: [4, 0, 4], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "X", axisPosition: "END", online: 1b, powered: 1b}}},
        {pos: [5, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 1106867L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsSouth: 1, channelsWest: 1, connections: ["south", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 1106867L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 1, channelsNorth: 1, channelsSouth: 0, connections: ["north", "south", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 1106867L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 1, channelsNorth: 0, channelsWest: 1, connections: ["north", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 0, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 1106867L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 7, channelsWest: 7, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 0, 1], state: "ae2:spatial_io_port{facing:north,powered:true,spin:0}", nbt: {id: "ae2:spatial_io_port", inv: {item0: {}, item1: {}}, lastRedstoneState: 1, proxy: {g: 1106867L, p: 0}, visual: {}}},
        {pos: [6, 0, 2], state: "ae2:energy_cell{fullness:4}", nbt: {id: "ae2:energy_cell", internalCurrentPower: 200000.0d, proxy: {g: 1106867L, p: 0}, visual: {}}},
        {pos: [6, 0, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 1106867L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsNorth: 3, channelsSouth: 3, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 0, 4], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 1106867L, p: 0}, visual: {}}},
        {pos: [0, 1, 4], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "Y", axisPosition: "START", online: 1b, powered: 1b}}},
        {pos: [6, 1, 1], state: "minecraft:oak_button{face:floor,facing:south,powered:false}"},
        {pos: [0, 2, 0], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "Z", axisPosition: "START", online: 1b, powered: 1b}}},
        {pos: [0, 2, 1], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "Z", axisPosition: "MIDDLE", online: 1b, powered: 1b}}},
        {pos: [0, 2, 2], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "Z", axisPosition: "END", online: 1b, powered: 1b}}},
        {pos: [0, 2, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 1106867L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 1, channelsSouth: 1, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 2, 4], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "Y", axisPosition: "MIDDLE", online: 1b, powered: 1b}}},
        {pos: [1, 2, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 1106867L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 1, channelsWest: 1, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 2, 4], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "X", axisPosition: "START", online: 1b, powered: 1b}}},
        {pos: [3, 2, 4], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "X", axisPosition: "MIDDLE", online: 1b, powered: 1b}}},
        {pos: [4, 2, 4], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "X", axisPosition: "END", online: 1b, powered: 1b}}},
        {pos: [0, 3, 4], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "Y", axisPosition: "MIDDLE", online: 1b, powered: 1b}}},
        {pos: [0, 4, 0], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "Z", axisPosition: "START", online: 1b, powered: 1b}}},
        {pos: [0, 4, 1], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "Z", axisPosition: "MIDDLE", online: 1b, powered: 1b}}},
        {pos: [0, 4, 2], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "Z", axisPosition: "END", online: 1b, powered: 1b}}},
        {pos: [0, 4, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 1106867L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 1, channelsSouth: 1, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 4, 4], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "Y", axisPosition: "END", online: 1b, powered: 1b}}},
        {pos: [1, 4, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 1106867L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 1, channelsWest: 1, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 4, 4], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "X", axisPosition: "START", online: 1b, powered: 1b}}},
        {pos: [3, 4, 4], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "X", axisPosition: "MIDDLE", online: 1b, powered: 1b}}},
        {pos: [4, 4, 4], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 1106867L, p: 0}, visual: {axis: "X", axisPosition: "END", online: 1b, powered: 1b}}}
    ],
    entities: [],
    palette: [
        "minecraft:oak_button{face:floor,facing:south,powered:false}",
        "ae2:spatial_pylon{powered_on:true}",
        "ae2:cable_bus{light_level:0,waterlogged:false}",
        "ae2:spatial_io_port{facing:north,powered:true,spin:0}",
        "ae2:energy_cell{fullness:4}",
        "ae2:controller{state:online,type:block}"
    ]
}