{
    DataVersion: 3337,
    size: [3, 3, 1],
    data: [
        {pos: [0, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27333L, k: -1L, p: 2}, id: "ae2:fluix_smart_dense_cable", visual: {channelsEast: 6, channelsUp: 6, connections: ["up", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27333L, k: -1L, p: 2}, id: "ae2:fluix_smart_dense_cable", visual: {channelsDown: 6, channelsEast: 6, channelsUp: 6, channelsWest: 6, connections: ["up", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27333L, k: -1L, p: 2}, id: "ae2:fluix_smart_dense_cable", visual: {channelsUp: 6, channelsWest: 6, connections: ["up", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 1, 0], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 27333L, k: -1L, p: 2}, id: "ae2:orange_smart_cable", visual: {channelsDown: 6, channelsUp: 6, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {gn: {g: 27333L, k: -1L, p: 2}, id: "ae2:pattern_access_terminal", show_pattern_providers: "VISIBLE", spin: 0b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [1, 1, 0], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 27333L, k: -1L, p: 2}, id: "ae2:green_smart_cable", visual: {channelsDown: 6, channelsUp: 6, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {filter_type: "ALL", gn: {g: 27333L, k: -1L, p: 2}, id: "ae2:pattern_encoding_terminal", mode: "CRAFTING", sort_by: "NAME", sort_direction: "ASCENDING", spin: 0b, substitute: 0b, substituteFluids: 1b, view_mode: "ALL", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [2, 1, 0], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 27333L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsDown: 6, channelsUp: 6, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {filter_type: "ALL", gn: {g: 27333L, k: -1L, p: 2}, id: "ae2:terminal", sort_by: "NAME", sort_direction: "ASCENDING", spin: 0b, view_mode: "ALL", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [0, 2, 0], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 27333L, k: -1L, p: 2}, id: "ae2:orange_smart_cable", visual: {channelsDown: 6, connections: ["down"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {filter_type: "ALL", gn: {g: 27333L, k: -1L, p: 2}, id: "ae2:crafting_terminal", sort_by: "NAME", sort_direction: "ASCENDING", spin: 0b, view_mode: "ALL", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [1, 2, 0], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 27333L, k: -1L, p: 2}, id: "ae2:green_smart_cable", visual: {channelsDown: 6, connections: ["down"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {gn: {g: 27333L, k: -1L, p: 2}, id: "ae2:pattern_access_terminal", show_pattern_providers: "VISIBLE", spin: 0b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [2, 2, 0], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 27333L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsDown: 6, connections: ["down"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {filter_type: "ALL", gn: {g: 27333L, k: -1L, p: 2}, id: "ae2:crafting_terminal", sort_by: "NAME", sort_direction: "ASCENDING", spin: 0b, view_mode: "ALL", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}}
    ],
    entities: [],
    palette: [
        "ae2:cable_bus{light_level:0,waterlogged:false}",
        "ae2:cable_bus{light_level:9,waterlogged:false}"
    ]
}