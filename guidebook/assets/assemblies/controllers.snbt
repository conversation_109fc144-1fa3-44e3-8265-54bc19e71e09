{
    DataVersion: 3337,
    size: [15, 5, 5],
    data: [
        {pos: [0, 0, 0], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 24196L, k: -1L, p: 0}, visual: {}}},
        {pos: [0, 0, 1], state: "ae2:controller{state:online,type:column_z}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 24196L, k: -1L, p: 0}, visual: {}}},
        {pos: [0, 0, 2], state: "ae2:controller{state:online,type:column_z}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 24196L, k: -1L, p: 0}, visual: {}}},
        {pos: [0, 0, 3], state: "ae2:controller{state:online,type:column_z}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 24196L, k: -1L, p: 0}, visual: {}}},
        {pos: [0, 0, 4], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 24196L, k: -1L, p: 0}, visual: {}}},
        {pos: [1, 0, 0], state: "ae2:controller{state:online,type:column_x}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 24196L, k: -1L, p: 0}, visual: {}}},
        {pos: [1, 0, 4], state: "ae2:controller{state:online,type:column_x}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 24196L, k: -1L, p: 0}, visual: {}}},
        {pos: [2, 0, 0], state: "ae2:controller{state:online,type:column_x}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 24196L, k: -1L, p: 0}, visual: {}}},
        {pos: [2, 0, 4], state: "ae2:controller{state:online,type:column_x}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 24196L, k: -1L, p: 0}, visual: {}}},
        {pos: [3, 0, 0], state: "ae2:controller{state:online,type:column_x}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 24196L, k: -1L, p: 0}, visual: {}}},
        {pos: [3, 0, 4], state: "ae2:controller{state:online,type:column_x}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 24196L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 0, 0], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 24196L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 0, 1], state: "ae2:controller{state:online,type:column_z}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 24196L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 0, 2], state: "ae2:controller{state:online,type:column_z}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 24196L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 0, 3], state: "ae2:controller{state:online,type:column_z}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 24196L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 0, 4], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 24196L, k: -1L, p: 0}, visual: {}}},
        {pos: [6, 0, 4], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 83486L, k: -1L, p: 0}, visual: {}}},
        {pos: [7, 0, 4], state: "ae2:controller{state:online,type:column_x}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 83486L, k: -1L, p: 0}, visual: {}}},
        {pos: [8, 0, 2], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 83486L, k: -1L, p: 0}, visual: {}}},
        {pos: [8, 0, 3], state: "ae2:controller{state:online,type:column_z}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 83486L, k: -1L, p: 0}, visual: {}}},
        {pos: [8, 0, 4], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 83486L, k: -1L, p: 0}, visual: {}}},
        {pos: [10, 0, 0], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 83483L, k: -1L, p: 0}, visual: {}}},
        {pos: [10, 0, 1], state: "ae2:controller{state:online,type:column_z}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 83483L, k: -1L, p: 0}, visual: {}}},
        {pos: [10, 0, 2], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 83483L, k: -1L, p: 0}, visual: {}}},
        {pos: [11, 0, 0], state: "ae2:controller{state:online,type:column_x}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 83483L, k: -1L, p: 0}, visual: {}}},
        {pos: [11, 0, 2], state: "ae2:controller{state:online,type:column_x}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 83483L, k: -1L, p: 0}, visual: {}}},
        {pos: [12, 0, 0], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 83483L, k: -1L, p: 0}, visual: {}}},
        {pos: [12, 0, 1], state: "ae2:controller{state:online,type:column_z}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 83483L, k: -1L, p: 0}, visual: {}}},
        {pos: [12, 0, 2], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 70L, k: -1L, p: 0}, visual: {}}},
        {pos: [14, 0, 1], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25562L, k: -1L, p: 1}, visual: {}}},
        {pos: [14, 0, 4], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25301L, k: -1L, p: 0}, visual: {}}},
        {pos: [0, 1, 0], state: "ae2:controller{state:online,type:column_y}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 24196L, k: -1L, p: 0}, visual: {}}},
        {pos: [0, 1, 4], state: "ae2:controller{state:online,type:column_y}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 72L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 1, 0], state: "ae2:controller{state:online,type:column_y}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 72L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 1, 4], state: "ae2:controller{state:online,type:column_y}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 72L, k: -1L, p: 0}, visual: {}}},
        {pos: [8, 1, 4], state: "ae2:controller{state:online,type:column_y}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25011L, k: -1L, p: 0}, visual: {}}},
        {pos: [11, 1, 4], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 69L, k: -1L, p: 0}, visual: {}}},
        {pos: [14, 1, 4], state: "ae2:controller{state:online,type:column_y}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25301L, k: -1L, p: 0}, visual: {}}},
        {pos: [0, 2, 0], state: "ae2:controller{state:online,type:column_y}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25001L, k: -1L, p: 0}, visual: {}}},
        {pos: [0, 2, 4], state: "ae2:controller{state:online,type:column_y}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25001L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 2, 0], state: "ae2:controller{state:online,type:column_y}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25001L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 2, 4], state: "ae2:controller{state:online,type:column_y}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25001L, k: -1L, p: 0}, visual: {}}},
        {pos: [8, 2, 4], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25011L, k: -1L, p: 0}, visual: {}}},
        {pos: [10, 2, 4], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 69L, k: -1L, p: 0}, visual: {}}},
        {pos: [11, 2, 4], state: "ae2:controller{state:online,type:column_x}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 69L, k: -1L, p: 0}, visual: {}}},
        {pos: [12, 2, 4], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 69L, k: -1L, p: 0}, visual: {}}},
        {pos: [14, 2, 4], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25301L, k: -1L, p: 0}, visual: {}}},
        {pos: [0, 3, 0], state: "ae2:controller{state:online,type:column_y}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25001L, k: -1L, p: 0}, visual: {}}},
        {pos: [0, 3, 4], state: "ae2:controller{state:online,type:column_y}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25001L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 3, 0], state: "ae2:controller{state:online,type:column_y}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25001L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 3, 4], state: "ae2:controller{state:online,type:column_y}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25001L, k: -1L, p: 0}, visual: {}}},
        {pos: [0, 4, 0], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25001L, k: -1L, p: 0}, visual: {}}},
        {pos: [0, 4, 1], state: "ae2:controller{state:online,type:column_z}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25001L, k: -1L, p: 0}, visual: {}}},
        {pos: [0, 4, 2], state: "ae2:controller{state:online,type:column_z}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25001L, k: -1L, p: 0}, visual: {}}},
        {pos: [0, 4, 3], state: "ae2:controller{state:online,type:column_z}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25001L, k: -1L, p: 0}, visual: {}}},
        {pos: [0, 4, 4], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25001L, k: -1L, p: 0}, visual: {}}},
        {pos: [1, 4, 0], state: "ae2:controller{state:online,type:column_x}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25001L, k: -1L, p: 0}, visual: {}}},
        {pos: [1, 4, 4], state: "ae2:controller{state:online,type:column_x}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25001L, k: -1L, p: 0}, visual: {}}},
        {pos: [2, 4, 0], state: "ae2:controller{state:online,type:column_x}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25001L, k: -1L, p: 0}, visual: {}}},
        {pos: [2, 4, 4], state: "ae2:controller{state:online,type:column_x}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25001L, k: -1L, p: 0}, visual: {}}},
        {pos: [3, 4, 0], state: "ae2:controller{state:online,type:column_x}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25001L, k: -1L, p: 0}, visual: {}}},
        {pos: [3, 4, 4], state: "ae2:controller{state:online,type:column_x}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25001L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 4, 0], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25001L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 4, 1], state: "ae2:controller{state:online,type:column_z}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25001L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 4, 2], state: "ae2:controller{state:online,type:column_z}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25001L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 4, 3], state: "ae2:controller{state:online,type:column_z}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25001L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 4, 4], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 25001L, k: -1L, p: 0}, visual: {}}}
    ],
    entities: [],
    palette: [
        "ae2:controller{state:online,type:block}",
        "ae2:controller{state:online,type:column_z}",
        "ae2:controller{state:online,type:column_x}",
        "ae2:creative_energy_cell",
        "ae2:controller{state:online,type:column_y}"
    ]
}