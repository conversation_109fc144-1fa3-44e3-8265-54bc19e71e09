{
    DataVersion: 3460,
    size: [6, 3, 3],
    data: [
        {pos: [1, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 621904L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 2, channelsSouth: 2, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 621904L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 0, channelsNorth: 2, channelsSouth: 2, connections: ["north", "south", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 621904L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 2, channelsUp: 2, connections: ["up", "north"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 621916L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 2, channelsSouth: 2, connections: ["south", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {gn: {g: 621916L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 621925L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {gn: {g: 621916L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 621904L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [2, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 621916L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsNorth: 2, connections: ["north"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {config: [{"#": 0L, "#c": "ae2:i", id: "ae2:flawed_budding_quartz"}], fuzzy_mode: "IGNORE_ALL", gn: {g: 621916L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [3, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 621916L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 2, channelsWest: 2, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 621916L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsWest: 2, connections: ["west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {fuzzy_mode: "IGNORE_ALL", gn: {g: 621916L, p: 0}, id: "ae2:formation_plane", place_block: "YES", priority: 0, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [1, 1, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 621904L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 2, channelsEast: 1, channelsUp: 1, connections: ["down", "up", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 621925L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsDown: 2, channelsEast: 2, connections: ["down", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", south: {access: "READ_WRITE", config: [{"#": 0L, "#c": "ae2:i", id: "ae2:quartz_block"}], filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 621925L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, up: {gn: {g: 621925L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 621930L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [2, 1, 2], state: "ae2:interface", nbt: {config: [{"#": 1L, "#c": "ae2:i", id: "ae2:flawed_budding_quartz"}], fuzzy_mode: "IGNORE_ALL", id: "ae2:interface", priority: 0, proxy: {g: 621904L, p: 0}, storage: [{"#": 1L, "#c": "ae2:i", id: "ae2:flawed_budding_quartz"}], upgrades: [{Count: 1b, Slot: 0, id: "ae2:crafting_card"}], visual: {}}},
        {pos: [3, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 621925L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsWest: 2, connections: ["west"], missingChannel: 0b, powered: 1b}}, east: {Enchantments: [{id: "minecraft:silk_touch", lvl: 1s}], gn: {g: 621925L, p: 0}, id: "ae2:annihilation_plane", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 1, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 621904L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 0, channelsWest: 0, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 1, 0], state: "ae2:growth_accelerator{facing:west,powered:true}", nbt: {id: "ae2:growth_accelerator", proxy: {g: 621904L, p: 0}, visual: {}}},
        {pos: [4, 1, 1], state: "ae2:flawed_budding_quartz"},
        {pos: [4, 1, 2], state: "ae2:growth_accelerator{facing:east,powered:true}", nbt: {id: "ae2:growth_accelerator", proxy: {g: 621904L, p: 0}, visual: {}}},
        {pos: [5, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 621904L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsSouth: 0, channelsWest: 0, connections: ["south", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 1, 1], state: "ae2:growth_accelerator{facing:north,powered:true}", nbt: {id: "ae2:growth_accelerator", proxy: {g: 621904L, p: 0}, visual: {}}},
        {pos: [5, 1, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 621904L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 0, channelsWest: 0, connections: ["north", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 2, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 621904L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 1, connections: ["down"], missingChannel: 0b, powered: 1b}}, east: {access: "READ_WRITE", config: [{"#": 0L, "#c": "ae2:i", id: "ae2:certus_quartz_crystal"}], filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 621904L, p: 0}, id: "ae2:storage_bus", priority: 1, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 2, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 621930L, p: 0}, id: "ae2:red_smart_cable", visual: {channelsDown: 2, channelsEast: 2, connections: ["down", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", south: {access: "READ_WRITE", config: [{"#": 0L, "#c": "ae2:i", id: "ae2:certus_quartz_crystal"}], filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 621930L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [2, 2, 2], state: "minecraft:barrel{facing:up,open:false}", nbt: {Items: [{Count: 64b, Slot: 0b, id: "ae2:certus_quartz_crystal"}, {Count: 36b, Slot: 1b, id: "ae2:certus_quartz_crystal"}], id: "minecraft:barrel"}},
        {pos: [3, 2, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 621930L, p: 0}, id: "ae2:red_smart_cable", visual: {channelsWest: 2, connections: ["west"], missingChannel: 0b, powered: 1b}}, east: {Enchantments: [{id: "minecraft:fortune", lvl: 3s}], gn: {g: 621930L, p: 0}, id: "ae2:annihilation_plane", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 2, 1], state: "ae2:large_quartz_bud{facing:up,waterlogged:false}"}
    ],
    entities: [],
    palette: [
        "ae2:flawed_budding_quartz",
        "ae2:large_quartz_bud{facing:up,waterlogged:false}",
        "ae2:cable_bus{light_level:0,waterlogged:false}",
        "ae2:interface",
        "ae2:growth_accelerator{facing:west,powered:true}",
        "ae2:growth_accelerator{facing:east,powered:true}",
        "ae2:growth_accelerator{facing:north,powered:true}",
        "minecraft:barrel{facing:up,open:false}"
    ]
}