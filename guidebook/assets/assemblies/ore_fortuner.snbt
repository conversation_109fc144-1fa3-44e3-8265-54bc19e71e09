{
    DataVersion: 3337,
    size: [5, 1, 3],
    data: [
        {pos: [0, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 227L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 3, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", south: {gn: {g: 227L, k: -1L, p: 0}, id: "ae2:annihilation_plane", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [0, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 243L, k: -1L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsEast: 3, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 243L, k: -1L, p: 0}, id: "ae2:formation_plane", place_block: "YES", priority: 0, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [1, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 227L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 3, channelsWest: 3, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", south: {gn: {g: 227L, k: -1L, p: 0}, id: "ae2:annihilation_plane", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [1, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 243L, k: -1L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsEast: 3, channelsWest: 3, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 243L, k: -1L, p: 0}, id: "ae2:formation_plane", place_block: "YES", priority: 0, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [2, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 227L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsSouth: 3, channelsWest: 3, connections: ["south", "west"], missingChannel: 0b, powered: 1b}}, east: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 227L, k: -1L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 420L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 0, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {gn: {g: 420L, k: -1L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 227L, k: -1L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}, south: {gn: {g: 420L, k: -1L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 243L, k: -1L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [2, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 243L, k: -1L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsNorth: 3, channelsWest: 3, connections: ["north", "west"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 243L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}], visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 0, 0], state: "minecraft:barrel{facing:up,open:false}", nbt: {Items: [{Count: 64b, Slot: 0b, id: "minecraft:cobblestone"}, {Count: 64b, Slot: 1b, id: "minecraft:cobblestone"}, {Count: 64b, Slot: 2b, id: "minecraft:cobblestone"}, {Count: 64b, Slot: 3b, id: "minecraft:cobblestone"}, {Count: 64b, Slot: 4b, id: "minecraft:cobblestone"}, {Count: 64b, Slot: 5b, id: "minecraft:cobblestone"}, {Count: 64b, Slot: 6b, id: "minecraft:cobblestone"}, {Count: 64b, Slot: 7b, id: "minecraft:cobblestone"}, {Count: 64b, Slot: 8b, id: "minecraft:cobblestone"}, {Count: 64b, Slot: 9b, id: "minecraft:cobblestone"}, {Count: 64b, Slot: 10b, id: "minecraft:cobblestone"}, {Count: 64b, Slot: 11b, id: "minecraft:cobblestone"}, {Count: 64b, Slot: 12b, id: "minecraft:cobblestone"}, {Count: 64b, Slot: 13b, id: "minecraft:cobblestone"}, {Count: 64b, Slot: 14b, id: "minecraft:cobblestone"}, {Count: 64b, Slot: 15b, id: "minecraft:cobblestone"}], id: "minecraft:barrel"}},
        {pos: [3, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 420L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 0, channelsEast: 0, channelsWest: 0, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 0, 2], state: "minecraft:barrel{facing:up,open:false}", nbt: {Items: [], id: "minecraft:barrel"}}
    ],
    entities: [],
    palette: [
        "ae2:cable_bus{light_level:0,waterlogged:false}",
        "minecraft:barrel{facing:up,open:false}"
    ]
}