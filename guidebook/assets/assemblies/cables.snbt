{
    DataVersion: 3337,
    size: [15, 3, 1],
    data: [
        {pos: [0, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83426L, k: -1L, p: 0}, id: "ae2:fluix_smart_dense_cable", visual: {channelsEast: 0, channelsUp: 0, connections: ["up", "east"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83426L, k: -1L, p: 0}, id: "ae2:fluix_smart_dense_cable", visual: {channelsEast: 0, channelsUp: 0, channelsWest: 0, connections: ["up", "west", "east"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83426L, k: -1L, p: 0}, id: "ae2:fluix_smart_dense_cable", visual: {channelsEast: 0, channelsUp: 0, channelsWest: 0, connections: ["up", "west", "east"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83426L, k: -1L, p: 0}, id: "ae2:fluix_covered_dense_cable", visual: {channelsEast: 0, channelsUp: 0, channelsWest: 0, connections: ["up", "west", "east"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83426L, k: -1L, p: 0}, id: "ae2:fluix_covered_dense_cable", visual: {channelsEast: 0, channelsUp: 0, channelsWest: 0, connections: ["up", "west", "east"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:fluix_covered_dense_cable", visual: {channelsEast: 0, channelsUp: 0, channelsWest: 0, connections: ["up", "west", "east"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:fluix_smart_cable", visual: {channelsEast: 0, channelsUp: 0, channelsWest: 0, connections: ["up", "west", "east"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:fluix_smart_cable", visual: {channelsEast: 0, channelsUp: 0, channelsWest: 0, connections: ["up", "west", "east"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:fluix_smart_cable", visual: {channelsEast: 0, channelsUp: 0, channelsWest: 0, connections: ["up", "west", "east"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:fluix_covered_cable", visual: {channelsEast: 0, channelsUp: 0, channelsWest: 0, connections: ["up", "west", "east"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:fluix_covered_cable", visual: {channelsEast: 0, channelsUp: 0, channelsWest: 0, connections: ["up", "west", "east"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [11, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:fluix_covered_cable", visual: {channelsEast: 0, channelsUp: 0, channelsWest: 0, connections: ["up", "west", "east"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [12, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:fluix_glass_cable", visual: {channelsEast: 0, channelsUp: 0, channelsWest: 0, connections: ["up", "west", "east"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [13, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:fluix_glass_cable", visual: {channelsEast: 0, channelsUp: 0, channelsWest: 0, connections: ["up", "west", "east"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [14, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:fluix_glass_cable", visual: {channelsUp: 0, channelsWest: 0, connections: ["up", "west"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:green_smart_dense_cable", visual: {channelsDown: 0, channelsUp: 0, connections: ["down", "up"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsDown: 0, channelsUp: 0, connections: ["down", "up"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:light_gray_smart_dense_cable", visual: {channelsDown: 0, channelsUp: 0, connections: ["down", "up"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:green_covered_dense_cable", visual: {channelsDown: 0, channelsUp: 0, connections: ["down", "up"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:blue_covered_dense_cable", visual: {channelsDown: 0, channelsUp: 0, connections: ["down", "up"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:white_covered_dense_cable", visual: {channelsDown: 0, channelsUp: 0, connections: ["down", "up"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsDown: 0, channelsUp: 0, connections: ["down", "up"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 0, channelsUp: 0, connections: ["down", "up"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:white_smart_cable", visual: {channelsDown: 0, channelsUp: 0, connections: ["down", "up"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:green_covered_cable", visual: {channelsDown: 0, channelsUp: 0, connections: ["down", "up"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:blue_covered_cable", visual: {channelsDown: 0, channelsUp: 0, connections: ["down", "up"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [11, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:white_covered_cable", visual: {channelsDown: 0, channelsUp: 0, connections: ["down", "up"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [12, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:green_glass_cable", visual: {channelsDown: 0, channelsUp: 0, connections: ["down", "up"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [13, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:blue_glass_cable", visual: {channelsDown: 0, channelsUp: 0, connections: ["down", "up"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [14, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:white_glass_cable", visual: {channelsDown: 0, channelsUp: 0, connections: ["down", "up"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:green_smart_dense_cable", visual: {channelsDown: 0, connections: ["down"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsDown: 0, connections: ["down"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:light_gray_smart_dense_cable", visual: {channelsDown: 0, connections: ["down"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:green_covered_dense_cable", visual: {channelsDown: 0, connections: ["down"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:blue_covered_dense_cable", visual: {channelsDown: 0, connections: ["down"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:white_covered_dense_cable", visual: {channelsDown: 0, connections: ["down"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsDown: 0, connections: ["down"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 0, connections: ["down"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:white_smart_cable", visual: {channelsDown: 0, connections: ["down"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:green_covered_cable", visual: {channelsDown: 0, connections: ["down"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:blue_covered_cable", visual: {channelsDown: 0, connections: ["down"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [11, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:white_covered_cable", visual: {channelsDown: 0, connections: ["down"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [12, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:green_glass_cable", visual: {channelsDown: 0, connections: ["down"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [13, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:blue_glass_cable", visual: {channelsDown: 0, connections: ["down"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [14, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 121L, k: -1L, p: 0}, id: "ae2:white_glass_cable", visual: {channelsDown: 0, connections: ["down"], missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}}
    ],
    entities: [],
    palette: [
        "ae2:cable_bus{light_level:0,waterlogged:false}"
    ]
}