{
    DataVersion: 3337,
    size: [3, 1, 1],
    data: [
        {pos: [0, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 135996L, k: -1L, p: 0}, id: "ae2:fluix_smart_dense_cable", visual: {channelsEast: 24, channelsWest: 24, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 135996L, k: -1L, p: 0}, id: "ae2:fluix_smart_dense_cable", visual: {channelsEast: 24, channelsWest: 24, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 135996L, k: -1L, p: 0}, id: "ae2:fluix_smart_dense_cable", visual: {channelsDown: 24, channelsWest: 24, connections: ["west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}}
    ],
    entities: [],
    palette: [
        "ae2:cable_bus{light_level:0,waterlogged:false}"
    ]
}