{
    DataVersion: 3337,
    size: [8, 4, 5],
    data: [
        {pos: [0, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83535L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsEast: 4, connections: ["east"], missingChannel: 0b, powered: 1b}}, down: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83535L, k: -1L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83535L, k: -1L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83535L, k: -1L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, up: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83535L, k: -1L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [1, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83535L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsEast: 8, channelsWest: 4, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83535L, k: -1L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83535L, k: -1L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83535L, k: -1L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, up: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83535L, k: -1L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [2, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83535L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsSouth: 4, connections: ["south"], missingChannel: 0b, powered: 1b}}, down: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83535L, k: -1L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83535L, k: -1L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83535L, k: -1L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83535L, k: -1L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [2, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83535L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 4, channelsSouth: 8, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, down: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83535L, k: -1L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83535L, k: -1L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83535L, k: -1L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83535L, k: -1L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [2, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83535L, k: -1L, p: 1}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 24, channelsNorth: 8, channelsSouth: 8, channelsWest: 8, connections: ["north", "south", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 0, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 4142L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 8, channelsSouth: 4, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, down: {fuzzy_mode: "IGNORE_ALL", gn: {g: 4142L, k: -1L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 4142L, k: -1L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {fuzzy_mode: "IGNORE_ALL", gn: {g: 4142L, k: -1L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 4142L, k: -1L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [2, 0, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 4142L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 4, connections: ["north"], missingChannel: 0b, powered: 1b}}, down: {fuzzy_mode: "IGNORE_ALL", gn: {g: 4142L, k: -1L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 4142L, k: -1L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {fuzzy_mode: "IGNORE_ALL", gn: {g: 4142L, k: -1L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 4142L, k: -1L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [3, 0, 1], state: "minecraft:smooth_stone"},
        {pos: [3, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 4142L, k: -1L, p: 1}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 24, channelsWest: 24, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, facadeUp: {Count: 1b, id: "ae2:facade", tag: {item: "ae2:quartz_glass"}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 0, 3], state: "minecraft:smooth_stone"},
        {pos: [4, 0, 1], state: "minecraft:smooth_stone"},
        {pos: [4, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 4142L, k: -1L, p: 1}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 29, channelsUp: 5, channelsWest: 24, connections: ["up", "west", "east"], missingChannel: 0b, powered: 1b}}, facadeUp: {Count: 1b, id: "ae2:facade", tag: {item: "minecraft:smooth_stone"}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 0, 3], state: "minecraft:smooth_stone"},
        {pos: [5, 0, 1], state: "minecraft:smooth_stone"},
        {pos: [5, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 4142L, k: -1L, p: 1}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 29, channelsWest: 29, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, facadeUp: {Count: 1b, id: "ae2:facade", tag: {item: "ae2:quartz_glass"}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 0, 3], state: "minecraft:smooth_stone"},
        {pos: [6, 0, 1], state: "minecraft:smooth_stone"},
        {pos: [6, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 4142L, k: -1L, p: 1}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 29, channelsWest: 29, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, facadeUp: {Count: 1b, id: "ae2:facade", tag: {item: "ae2:quartz_glass"}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 0, 3], state: "minecraft:smooth_stone"},
        {pos: [7, 0, 1], state: "minecraft:smooth_stone"},
        {pos: [7, 0, 2], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 4142L, k: -1L, p: 1}, visual: {}}},
        {pos: [7, 0, 3], state: "minecraft:smooth_stone"},
        {pos: [4, 1, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 4142L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsDown: 5, channelsUp: 5, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 2, 2], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 4142L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsDown: 5, channelsUp: 1, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, east: {filter_type: "ALL", gn: {g: 4142L, k: -1L, p: 1}, id: "ae2:crafting_terminal", sort_by: "NAME", sort_direction: "ASCENDING", spin: 0b, view_mode: "ALL", visual: {missingChannel: 0b, powered: 1b}}, facadeDown: {Count: 1b, id: "ae2:facade", tag: {item: "minecraft:chiseled_quartz_block"}}, facadeEast: {Count: 1b, id: "ae2:facade", tag: {item: "minecraft:chiseled_quartz_block"}}, facadeNorth: {Count: 1b, id: "ae2:facade", tag: {item: "minecraft:chiseled_quartz_block"}}, facadeSouth: {Count: 1b, id: "ae2:facade", tag: {item: "minecraft:chiseled_quartz_block"}}, facadeUp: {Count: 1b, id: "ae2:facade", tag: {item: "minecraft:chiseled_quartz_block"}}, facadeWest: {Count: 1b, id: "ae2:facade", tag: {item: "minecraft:chiseled_quartz_block"}}, hasRedstone: 2, id: "ae2:cable_bus", north: {filter_type: "ALL", gn: {g: 4142L, k: -1L, p: 1}, id: "ae2:terminal", sort_by: "NAME", sort_direction: "ASCENDING", spin: 0b, view_mode: "ALL", visual: {missingChannel: 0b, powered: 1b}}, south: {filter_type: "ALL", gn: {g: 4142L, k: -1L, p: 1}, id: "ae2:pattern_encoding_terminal", mode: "CRAFTING", sort_by: "NAME", sort_direction: "ASCENDING", spin: 0b, substitute: 0b, substituteFluids: 1b, view_mode: "ALL", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {gn: {g: 4142L, k: -1L, p: 1}, id: "ae2:pattern_access_terminal", show_pattern_providers: "VISIBLE", spin: 0b, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [4, 3, 2], state: "ae2:wireless_access_point{facing:up,state:has_channel,waterlogged:false}", nbt: {id: "ae2:wireless_access_point", inv: {item0: {}}, proxy: {g: 4142L, k: -1L, p: 1}, visual: {}}}
    ],
    entities: [],
    palette: [
        "minecraft:smooth_stone",
        "ae2:cable_bus{light_level:0,waterlogged:false}",
        "ae2:controller{state:online,type:block}",
        "ae2:cable_bus{light_level:9,waterlogged:false}",
        "ae2:wireless_access_point{facing:up,state:has_channel,waterlogged:false}"
    ]
}