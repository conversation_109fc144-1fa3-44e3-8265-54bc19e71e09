{
    DataVersion: 3465,
    size: [8, 4, 5],
    data: [
        {pos: [2, 0, 0], state: "ae2:quartz_glass"},
        {pos: [2, 0, 1], state: "ae2:quartz_glass"},
        {pos: [2, 0, 2], state: "ae2:quartz_glass"},
        {pos: [2, 0, 3], state: "ae2:quartz_glass"},
        {pos: [2, 0, 4], state: "ae2:quartz_glass"},
        {pos: [3, 0, 0], state: "ae2:quartz_glass"},
        {pos: [3, 0, 1], state: "ae2:quartz_glass"},
        {pos: [3, 0, 2], state: "ae2:quartz_glass"},
        {pos: [3, 0, 3], state: "ae2:quartz_glass"},
        {pos: [3, 0, 4], state: "ae2:quartz_glass"},
        {pos: [4, 0, 0], state: "ae2:quartz_glass"},
        {pos: [4, 0, 1], state: "ae2:quartz_glass"},
        {pos: [4, 0, 2], state: "ae2:quartz_glass"},
        {pos: [4, 0, 3], state: "ae2:quartz_glass"},
        {pos: [4, 0, 4], state: "ae2:quartz_glass"},
        {pos: [5, 0, 0], state: "ae2:quartz_glass"},
        {pos: [5, 0, 1], state: "ae2:quartz_glass"},
        {pos: [5, 0, 2], state: "ae2:quartz_glass"},
        {pos: [5, 0, 3], state: "ae2:quartz_glass"},
        {pos: [5, 0, 4], state: "ae2:quartz_glass"},
        {pos: [6, 0, 0], state: "ae2:quartz_glass"},
        {pos: [6, 0, 1], state: "ae2:quartz_glass"},
        {pos: [6, 0, 2], state: "ae2:quartz_glass"},
        {pos: [6, 0, 3], state: "ae2:quartz_glass"},
        {pos: [6, 0, 4], state: "ae2:quartz_glass"},
        {pos: [7, 0, 0], state: "ae2:quartz_glass"},
        {pos: [7, 0, 1], state: "ae2:quartz_glass"},
        {pos: [7, 0, 2], state: "ae2:quartz_glass"},
        {pos: [7, 0, 3], state: "ae2:quartz_glass"},
        {pos: [7, 0, 4], state: "ae2:quartz_glass"},
        {pos: [2, 1, 0], state: "ae2:quartz_glass"},
        {pos: [2, 1, 1], state: "ae2:quartz_glass"},
        {pos: [2, 1, 2], state: "ae2:quartz_glass"},
        {pos: [2, 1, 3], state: "ae2:quartz_glass"},
        {pos: [2, 1, 4], state: "ae2:quartz_glass"},
        {pos: [3, 1, 0], state: "ae2:quartz_glass"},
        {pos: [3, 1, 1], state: "minecraft:lava{level:0}"},
        {pos: [3, 1, 2], state: "minecraft:cobblestone"},
        {pos: [3, 1, 3], state: "minecraft:stone_brick_stairs{facing:north,half:bottom,shape:straight,waterlogged:true}"},
        {pos: [3, 1, 4], state: "ae2:quartz_glass"},
        {pos: [4, 1, 0], state: "ae2:quartz_glass"},
        {pos: [4, 1, 1], state: "minecraft:lava{level:0}"},
        {pos: [4, 1, 2], state: "minecraft:cobblestone"},
        {pos: [4, 1, 3], state: "minecraft:stone_brick_stairs{facing:north,half:bottom,shape:straight,waterlogged:true}"},
        {pos: [4, 1, 4], state: "ae2:quartz_glass"},
        {pos: [5, 1, 0], state: "ae2:quartz_glass"},
        {pos: [5, 1, 1], state: "minecraft:lava{level:0}"},
        {pos: [5, 1, 2], state: "minecraft:cobblestone"},
        {pos: [5, 1, 3], state: "minecraft:stone_brick_stairs{facing:north,half:bottom,shape:straight,waterlogged:true}"},
        {pos: [5, 1, 4], state: "ae2:quartz_glass"},
        {pos: [6, 1, 0], state: "ae2:quartz_glass"},
        {pos: [6, 1, 1], state: "minecraft:lava{level:0}"},
        {pos: [6, 1, 2], state: "minecraft:cobblestone"},
        {pos: [6, 1, 3], state: "minecraft:stone_brick_stairs{facing:north,half:bottom,shape:straight,waterlogged:true}"},
        {pos: [6, 1, 4], state: "ae2:quartz_glass"},
        {pos: [7, 1, 0], state: "ae2:quartz_glass"},
        {pos: [7, 1, 1], state: "ae2:quartz_glass"},
        {pos: [7, 1, 2], state: "ae2:quartz_glass"},
        {pos: [7, 1, 3], state: "ae2:quartz_glass"},
        {pos: [7, 1, 4], state: "ae2:quartz_glass"},
        {pos: [0, 2, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 1086127L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 1, channelsWest: 1, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 2, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 1086127L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 0, channelsSouth: 1, channelsWest: 1, connections: ["south", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 2, 2], state: "ae2:interface", nbt: {fuzzy_mode: "IGNORE_ALL", id: "ae2:interface", priority: 0, proxy: {g: 1086127L, p: 0}, visual: {}}},
        {pos: [2, 2, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 1086127L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsWest: 0, connections: ["west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", south: {gn: {g: 1086127L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 1086145L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [2, 2, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 1086154L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 5, connections: ["east"], missingChannel: 0b, powered: 0b}}, hasRedstone: 1, id: "ae2:cable_bus", north: {gn: {g: 1086154L, p: 0}, id: "ae2:toggle_bus", outer: {g: 1086145L, p: 0}, visual: {missingChannel: 0b, on: 0b, powered: 0b}}, visual: {}, west: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 1086154L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 0b}}}},
        {pos: [3, 2, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 1086154L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 5, channelsWest: 5, connections: ["west", "east"], missingChannel: 0b, powered: 0b}}, down: {gn: {g: 1086154L, p: 0}, id: "ae2:annihilation_plane", visual: {missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 2, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 1086154L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 5, channelsWest: 5, connections: ["west", "east"], missingChannel: 0b, powered: 0b}}, down: {gn: {g: 1086154L, p: 0}, id: "ae2:annihilation_plane", visual: {missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 2, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 1086154L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 5, channelsWest: 5, connections: ["west", "east"], missingChannel: 0b, powered: 0b}}, down: {gn: {g: 1086154L, p: 0}, id: "ae2:annihilation_plane", visual: {missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 2, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 1086154L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsWest: 5, connections: ["west"], missingChannel: 0b, powered: 0b}}, down: {gn: {g: 1086154L, p: 0}, id: "ae2:annihilation_plane", visual: {missingChannel: 0b, powered: 0b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 3, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 1086127L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 0, channelsEast: 0, connections: ["down", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 3, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 1086127L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsWest: 0, connections: ["west"], missingChannel: 0b, powered: 1b}}, down: {config: [{"#": 0L, "#c": "ae2:i", id: "minecraft:cobblestone"}], craft_via_redstone: "NO", fuzzy_mode: "IGNORE_ALL", gn: {g: 1086127L, p: 0}, id: "ae2:level_emitter", lastReportedValue: 64L, prevState: 0b, redstone_emitter: "LOW_SIGNAL", reportingValue: 64L, visual: {missingChannel: 0b, on: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}}
    ],
    entities: [],
    palette: [
        "ae2:quartz_glass",
        "minecraft:cobblestone",
        "minecraft:lava{level:0}",
        "minecraft:stone_brick_stairs{facing:north,half:bottom,shape:straight,waterlogged:true}",
        "ae2:cable_bus{light_level:0,waterlogged:false}",
        "ae2:interface"
    ]
}