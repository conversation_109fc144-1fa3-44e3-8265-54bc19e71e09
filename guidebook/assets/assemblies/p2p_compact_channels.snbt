{
    DataVersion: 3337,
    size: [8, 4, 7],
    data: [
        {pos: [0, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsUp: 4, connections: ["up"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [1, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:light_blue_smart_cable", visual: {channelsUp: 4, connections: ["up"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [2, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsUp: 4, connections: ["up"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [2, 0, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 1, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", south: {freq: 24141s, gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 104754L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [2, 0, 6], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 104754L, k: -1L, p: 0}, visual: {}}},
        {pos: [3, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:light_blue_smart_cable", visual: {channelsUp: 4, connections: ["up"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [3, 0, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 2, channelsUp: 4, channelsWest: 1, connections: ["up", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", south: {freq: 10361s, gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 104754L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [3, 0, 6], state: "ae2:controller{state:online,type:column_x}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 104754L, k: -1L, p: 0}, visual: {}}},
        {pos: [4, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsUp: 4, connections: ["up"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [4, 0, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 1, channelsWest: 2, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", south: {freq: 12431s, gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 104754L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [4, 0, 6], state: "ae2:controller{state:online,type:column_x}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 104754L, k: -1L, p: 0}, visual: {}}},
        {pos: [5, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:light_blue_smart_cable", visual: {channelsUp: 4, connections: ["up"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [5, 0, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsWest: 1, connections: ["west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", south: {freq: 15369s, gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 104754L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [5, 0, 6], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 104754L, k: -1L, p: 0}, visual: {}}},
        {pos: [6, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsUp: 4, connections: ["up"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [7, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:light_blue_smart_cable", visual: {channelsUp: 4, connections: ["up"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [0, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 4, channelsUp: 8, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 1, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [1, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:light_blue_smart_cable", visual: {channelsDown: 4, channelsUp: 8, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [1, 1, 6], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 0, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 104716L, k: -1L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [2, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 4, channelsUp: 8, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [2, 1, 6], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsDown: 8, channelsUp: 8, channelsWest: 0, connections: ["down", "up", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:light_blue_smart_cable", visual: {channelsDown: 4, channelsUp: 8, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [3, 1, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 4, channelsUp: 4, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 1, 6], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:light_blue_smart_dense_cable", visual: {channelsDown: 8, channelsUp: 8, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 4, channelsUp: 8, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [4, 1, 6], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsDown: 8, channelsUp: 8, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:light_blue_smart_cable", visual: {channelsDown: 4, channelsUp: 8, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [5, 1, 6], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:light_blue_smart_dense_cable", visual: {channelsDown: 8, channelsUp: 8, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 4, channelsUp: 8, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [7, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:light_blue_smart_cable", visual: {channelsDown: 4, channelsUp: 8, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [0, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsDown: 8, channelsUp: 8, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:light_blue_smart_dense_cable", visual: {channelsDown: 8, channelsUp: 8, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 2, 6], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 0, channelsEast: 0, connections: ["down", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsDown: 8, channelsUp: 8, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 2, 6], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 1, channelsWest: 0, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {freq: 19652s, gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 104754L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:light_blue_smart_dense_cable", visual: {channelsDown: 8, channelsUp: 8, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 2, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 4, channelsSouth: 4, channelsUp: 8, connections: ["down", "up", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 2, 6], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 2, channelsNorth: 4, channelsWest: 1, connections: ["north", "west", "east"], missingChannel: 0b, powered: 1b}}, down: {freq: -8210s, gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 104754L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsDown: 8, channelsUp: 8, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 2, 6], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 1, channelsWest: 2, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {freq: -16182s, gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 104754L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:light_blue_smart_dense_cable", visual: {channelsDown: 8, channelsUp: 8, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 2, 6], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsWest: 1, connections: ["west"], missingChannel: 0b, powered: 1b}}, down: {freq: 28773s, gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 104754L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsDown: 8, channelsUp: 8, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104754L, k: -1L, p: 0}, id: "ae2:light_blue_smart_dense_cable", visual: {channelsDown: 8, channelsUp: 8, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 3, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 1, connections: ["east"], missingChannel: 0b, powered: 1b}}, down: {freq: 24141s, gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 104754L, k: -1L, p: 0}, output: 1b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 3, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 2, channelsWest: 1, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {freq: 10361s, gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 104754L, k: -1L, p: 0}, output: 1b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 3, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 3, channelsWest: 2, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {freq: 12431s, gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 104754L, k: -1L, p: 0}, output: 1b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 3, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 4, channelsSouth: 8, channelsWest: 3, connections: ["south", "west", "east"], missingChannel: 0b, powered: 1b}}, down: {freq: 15369s, gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 104754L, k: -1L, p: 0}, output: 1b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 3, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsNorth: 8, channelsSouth: 8, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 3, 2], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 104716L, k: -1L, p: 0}, visual: {}}},
        {pos: [3, 3, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsNorth: 8, channelsSouth: 8, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 3, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsNorth: 8, channelsSouth: 8, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 3, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 8, channelsNorth: 8, connections: ["down", "north"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 3, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 3, channelsWest: 4, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {freq: 19652s, gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 104754L, k: -1L, p: 0}, output: 1b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 3, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 2, channelsWest: 3, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {freq: -8210s, gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 104754L, k: -1L, p: 0}, output: 1b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 3, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 1, channelsWest: 2, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {freq: -16182s, gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 104754L, k: -1L, p: 0}, output: 1b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 3, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsWest: 1, connections: ["west"], missingChannel: 0b, powered: 1b}}, down: {freq: 28773s, gn: {g: 104716L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 104754L, k: -1L, p: 0}, output: 1b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}}
    ],
    entities: [],
    palette: [
        "ae2:cable_bus{light_level:0,waterlogged:false}",
        "ae2:controller{state:online,type:block}",
        "ae2:controller{state:online,type:column_x}",
        "ae2:creative_energy_cell"
    ]
}