{
    DataVersion: 3337,
    size: [5, 2, 1],
    data: [
        {pos: [0, 0, 0], state: "ae2:interface", nbt: {config: [{"#": 64L, "#c": "ae2:i", id: "ae2:blue_smart_cable"}, {"#": 64L, "#c": "ae2:i", id: "ae2:orange_smart_cable"}, {"#": 32L, "#c": "ae2:i", id: "ae2:blue_smart_dense_cable"}, {"#": 32L, "#c": "ae2:i", id: "ae2:orange_smart_dense_cable"}, {"#": 4L, "#c": "ae2:i", id: "minecraft:barrel"}], fuzzy_mode: "IGNORE_ALL", id: "ae2:interface", priority: 0, proxy: {g: 367L, k: -1L, p: 0}, storage: [{"#": 64L, "#c": "ae2:i", id: "ae2:blue_smart_cable"}, {"#": 64L, "#c": "ae2:i", id: "ae2:orange_smart_cable"}, {"#": 32L, "#c": "ae2:i", id: "ae2:blue_smart_dense_cable"}, {"#": 32L, "#c": "ae2:i", id: "ae2:orange_smart_dense_cable"}, {"#": 4L, "#c": "ae2:i", id: "minecraft:barrel"}], upgrades: [{Count: 1b, Slot: 0, id: "ae2:crafting_card"}], visual: {}}},
        {pos: [1, 0, 0], state: "ae2:interface", nbt: {config: [{"#": 64L, "#c": "ae2:i", id: "ae2:logic_processor"}, {"#": 32L, "#c": "ae2:i", id: "ae2:calculation_processor"}, {"#": 16L, "#c": "ae2:i", id: "ae2:engineering_processor"}, {"#": 48L, "#c": "ae2:i", id: "ae2:fluix_crystal"}, {"#": 64L, "#c": "ae2:i", id: "minecraft:stick"}, {"#": 64L, "#c": "ae2:i", id: "minecraft:oak_planks"}, {"#": 64L, "#c": "ae2:i", id: "minecraft:glass"}, {"#": 8L, "#c": "ae2:i", id: "ae2:annihilation_core"}, {"#": 8L, "#c": "ae2:i", id: "ae2:formation_core"}], fuzzy_mode: "IGNORE_ALL", id: "ae2:interface", priority: 0, proxy: {g: 367L, k: -1L, p: 0}, storage: [{"#": 64L, "#c": "ae2:i", id: "ae2:logic_processor"}, {"#": 32L, "#c": "ae2:i", id: "ae2:calculation_processor"}, {"#": 16L, "#c": "ae2:i", id: "ae2:engineering_processor"}, {"#": 48L, "#c": "ae2:i", id: "ae2:fluix_crystal"}, {"#": 64L, "#c": "ae2:i", id: "minecraft:stick"}, {"#": 64L, "#c": "ae2:i", id: "minecraft:oak_planks"}, {"#": 64L, "#c": "ae2:i", id: "minecraft:glass"}, {"#": 8L, "#c": "ae2:i", id: "ae2:annihilation_core"}, {"#": 8L, "#c": "ae2:i", id: "ae2:formation_core"}], upgrades: [{Count: 1b, Slot: 0, id: "ae2:crafting_card"}], visual: {}}},
        {pos: [2, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 367L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 4, channelsUp: 2, channelsWest: 2, connections: ["up", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 367L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsEast: 4, channelsWest: 4, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 367L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 1, connections: ["east"], missingChannel: 0b, powered: 1b}}, down: {access: "READ", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 367L, k: -1L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 367L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 2, channelsWest: 1, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {access: "READ", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 367L, k: -1L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 367L, k: -1L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 2, channelsWest: 2, connections: ["down", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}}
    ],
    entities: [],
    palette: [
        "ae2:interface",
        "ae2:cable_bus{light_level:0,waterlogged:false}",
    ]
}