{
    DataVersion: 3460,
    size: [9, 6, 2],
    data: [
        {pos: [0, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 444L, p: 0}, id: "ae2:purple_smart_cable", visual: {channelsEast: 2, channelsUp: 2, connections: ["up", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 444L, p: 0}, id: "ae2:purple_smart_cable", visual: {channelsEast: 2, channelsWest: 2, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 444L, p: 0}, id: "ae2:purple_smart_cable", visual: {channelsEast: 2, channelsWest: 2, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 444L, p: 0}, id: "ae2:purple_smart_cable", visual: {channelsEast: 2, channelsWest: 2, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {gn: {g: 444L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 180L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [4, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 26952L, p: 0}, id: "ae2:purple_smart_cable", visual: {channelsEast: 2, channelsWest: 2, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 26952L, p: 0}, id: "ae2:purple_smart_cable", visual: {channelsWest: 2, connections: ["west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 26952L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [0, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 26952L, p: 0}, id: "ae2:purple_smart_cable", visual: {channelsDown: 2, channelsUp: 2, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 266L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsEast: 2, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 266L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [2, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 266L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsUp: 2, channelsWest: 2, connections: ["up", "west"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 266L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}], visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 1, 0], state: "ae2:inscriber{facing:north,spin:0,waterlogged:false}", nbt: {auto_export: "NO", id: "ae2:inscriber", inscriber_separate_sides: "NO", internalCurrentPower: 0.0d, inv: {item0: {Count: 1b, id: "ae2:silicon_press"}, item1: {}, item2: {}, item3: {}}, proxy: {g: 180L, p: 0}, upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}, {Count: 1b, Slot: 2, id: "ae2:speed_card"}, {Count: 1b, Slot: 3, id: "ae2:speed_card"}], visual: {smash: 0b}}},
        {pos: [4, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 508378L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsUp: 6, connections: ["up"], missingChannel: 0b, powered: 1b}}, east: {gn: {g: 508378L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 83731L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {config: [{"#": 0L, "#c": "ae2:i", id: "ae2:silicon"}], craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {g: 508378L, p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}], visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [5, 1, 0], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", patterns: [{Count: 1b, Slot: 0, id: "ae2:processing_pattern", tag: {in: [{"#": 1L, "#c": "ae2:i", id: "ae2:silicon"}, {"#": 1L, "#c": "ae2:i", id: "minecraft:redstone"}, {"#": 1L, "#c": "ae2:i", id: "minecraft:gold_ingot"}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}], out: [{"#": 1L, "#c": "ae2:i", id: "ae2:logic_processor"}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}]}}, {Count: 1b, Slot: 1, id: "ae2:processing_pattern", tag: {in: [{"#": 1L, "#c": "ae2:i", id: "ae2:silicon"}, {"#": 1L, "#c": "ae2:i", id: "minecraft:redstone"}, {"#": 1L, "#c": "ae2:i", id: "ae2:certus_quartz_crystal"}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}], out: [{"#": 1L, "#c": "ae2:i", id: "ae2:calculation_processor"}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}]}}, {Count: 1b, Slot: 2, id: "ae2:processing_pattern", tag: {in: [{"#": 1L, "#c": "ae2:i", id: "ae2:silicon"}, {"#": 1L, "#c": "ae2:i", id: "minecraft:redstone"}, {"#": 1L, "#c": "ae2:i", id: "minecraft:diamond"}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}], out: [{"#": 1L, "#c": "ae2:i", id: "ae2:engineering_processor"}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}]}}], priority: 0, proxy: {g: 83731L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [6, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83731L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 1, channelsWest: 1, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 26952L, p: 0}, id: "ae2:purple_smart_cable", visual: {channelsDown: 2, connections: ["down"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 26952L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}], visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 2, 0], state: "ae2:inscriber{facing:north,spin:0,waterlogged:false}", nbt: {auto_export: "NO", id: "ae2:inscriber", inscriber_separate_sides: "NO", internalCurrentPower: 0.0d, inv: {item0: {}, item1: {}, item2: {}, item3: {}}, proxy: {g: 25621L, p: 0}, upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}, {Count: 1b, Slot: 2, id: "ae2:speed_card"}, {Count: 1b, Slot: 3, id: "ae2:speed_card"}], visual: {smash: 0b}}},
        {pos: [1, 2, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 508378L, p: 1}, id: "ae2:orange_smart_cable", visual: {channelsEast: 6, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {gn: {g: 508378L, p: 1}, id: "ae2:quartz_fiber", outer: {g: 25621L, p: 1}, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [2, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 508378L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 6, channelsSouth: 6, connections: ["south", "east"], missingChannel: 0b, powered: 1b}}, down: {gn: {g: 508378L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 266L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {gn: {g: 508378L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 83742L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {config: [{"#": 0L, "#c": "ae2:i", id: "minecraft:redstone"}], craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {g: 508378L, p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}], visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [2, 2, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 508378L, p: 1}, id: "ae2:orange_smart_cable", visual: {channelsNorth: 6, channelsWest: 6, connections: ["north", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 508378L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 6, channelsWest: 6, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {gn: {g: 508378L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 180L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {gn: {g: 508378L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 263L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [4, 2, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 508378L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 6, channelsUp: 6, channelsWest: 6, connections: ["down", "up", "west"], missingChannel: 0b, powered: 1b}}, east: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 508378L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 2, 0], state: "minecraft:barrel{facing:north,open:false}", nbt: {Items: [], id: "minecraft:barrel"}},
        {pos: [1, 3, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83742L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsEast: 4, connections: ["east"], missingChannel: 0b, powered: 1b}}, down: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 83742L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 3, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83742L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsDown: 4, channelsUp: 4, channelsWest: 4, connections: ["down", "up", "west"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83742L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}], visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 3, 0], state: "ae2:inscriber{facing:north,spin:0,waterlogged:false}", nbt: {auto_export: "NO", id: "ae2:inscriber", inscriber_separate_sides: "NO", internalCurrentPower: 0.0d, inv: {item0: {Count: 1b, id: "ae2:logic_processor_press"}, item1: {}, item2: {}, item3: {}}, proxy: {g: 263L, p: 0}, upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}, {Count: 1b, Slot: 2, id: "ae2:speed_card"}, {Count: 1b, Slot: 3, id: "ae2:speed_card"}], visual: {smash: 0b}}},
        {pos: [4, 3, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 253L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 6, channelsUp: 6, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {config: [{"#": 0L, "#c": "ae2:i", id: "minecraft:gold_ingot"}], craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {g: 253L, p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}], visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [2, 4, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 280L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsDown: 4, channelsUp: 4, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 280L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}], visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 4, 0], state: "ae2:inscriber{facing:north,spin:0,waterlogged:false}", nbt: {auto_export: "NO", id: "ae2:inscriber", inscriber_separate_sides: "NO", internalCurrentPower: 0.0d, inv: {item0: {Count: 1b, id: "ae2:calculation_processor_press"}, item1: {}, item2: {}, item3: {}}, proxy: {g: 263L, p: 0}, upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}, {Count: 1b, Slot: 2, id: "ae2:speed_card"}, {Count: 1b, Slot: 3, id: "ae2:speed_card"}], visual: {smash: 0b}}},
        {pos: [4, 4, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 253L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 6, channelsUp: 6, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {config: [{"#": 0L, "#c": "ae2:i", id: "ae2:certus_quartz_crystal"}], craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {g: 253L, p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}], visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [2, 5, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 280L, p: 0}, id: "ae2:green_smart_cable", visual: {channelsDown: 4, connections: ["down"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 280L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}], visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 5, 0], state: "ae2:inscriber{facing:north,spin:0,waterlogged:false}", nbt: {auto_export: "NO", id: "ae2:inscriber", inscriber_separate_sides: "NO", internalCurrentPower: 0.0d, inv: {item0: {Count: 1b, id: "ae2:engineering_processor_press"}, item1: {}, item2: {}, item3: {}}, proxy: {g: 263L, p: 0}, upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}, {Count: 1b, Slot: 2, id: "ae2:speed_card"}, {Count: 1b, Slot: 3, id: "ae2:speed_card"}], visual: {smash: 0b}}},
        {pos: [4, 5, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 253L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsDown: 6, connections: ["down"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {config: [{"#": 0L, "#c": "ae2:i", id: "minecraft:diamond"}], craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {g: 253L, p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", upgrades: [{Count: 1b, Slot: 0, id: "ae2:speed_card"}, {Count: 1b, Slot: 1, id: "ae2:speed_card"}], visual: {missingChannel: 0b, powered: 1b}}}}
    ],
    entities: [],
    palette: [
        "ae2:cable_bus{light_level:0,waterlogged:false}",
        "ae2:inscriber{facing:north,spin:0,waterlogged:false}",
        "ae2:pattern_provider{push_direction:all}",
        "minecraft:barrel{facing:north,open:false}"
    ]
}