{
    DataVersion: 3337,
    size: [6, 2, 4],
    data: [
        {pos: [0, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 508L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 5, channelsUp: 5, connections: ["up", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 508L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 5, channelsWest: 5, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 0, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 449L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 5, channelsUp: 5, connections: ["up", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 526L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsEast: 2, connections: ["east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {freq: 803s, gn: {g: 526L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 508L, k: -1L, p: 0}, output: 1b, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [2, 0, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 449L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 5, channelsWest: 5, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 526L, k: -1L, p: 0}, id: "ae2:orange_smart_cable", visual: {channelsWest: 2, connections: ["west"], missingChannel: 0b, powered: 1b}}, down: {gn: {g: 526L, k: -1L, p: 0}, id: "ae2:quartz_fiber", outer: {g: 528L, k: -1L, p: 0}, visual: {missingChannel: 0b, powered: 1b}}, east: {freq: 803s, gn: {g: 526L, k: -1L, p: 0}, id: "ae2:me_p2p_tunnel", outer: {g: 508L, k: -1L, p: 0}, output: 0b, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 0, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 449L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 5, channelsWest: 5, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 508L, k: -1L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 5, channelsWest: 5, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 0, 3], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 449L, k: -1L, p: 0}, visual: {}}},
        {pos: [5, 0, 0], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 508L, k: -1L, p: 0}, visual: {}}},
        {pos: [0, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 508L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsDown: 5, connections: ["down"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 508L, k: -1L, p: 2}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 1, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 508L, k: -1L, p: 2}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 508L, k: -1L, p: 2}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, up: {fuzzy_mode: "IGNORE_ALL", gn: {g: 508L, k: -1L, p: 2}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 508L, k: -1L, p: 2}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [1, 1, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 449L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsDown: 5, connections: ["down"], missingChannel: 0b, powered: 1b}}, east: {fuzzy_mode: "IGNORE_ALL", gn: {g: 449L, k: -1L, p: 2}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 1, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 449L, k: -1L, p: 2}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 449L, k: -1L, p: 2}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, up: {fuzzy_mode: "IGNORE_ALL", gn: {g: 449L, k: -1L, p: 2}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}, west: {fuzzy_mode: "IGNORE_ALL", gn: {g: 449L, k: -1L, p: 2}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}}}
    ],
    entities: [],
    palette: [
        "ae2:cable_bus{light_level:0,waterlogged:false}",
        "ae2:controller{state:online,type:block}"
    ]
}