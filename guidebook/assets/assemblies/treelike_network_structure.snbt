{
    DataVersion: 3460,
    size: [13, 6, 15],
    data: [
        {pos: [0, 0, 0], state: "ae2:1k_crafting_storage{formed:true,powered:true}", nbt: {core: 1b, crafting_scheduling_mode: "ANY", id: "ae2:crafting_storage", inventory: [], proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [0, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 1, channelsNorth: 1, channelsSouth: 2, connections: ["north", "south", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 0, 2], state: "ae2:1k_crafting_storage{formed:true,powered:true}", nbt: {core: 1b, crafting_scheduling_mode: "ANY", id: "ae2:crafting_storage", inventory: [], proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [0, 0, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 1, channelsNorth: 3, channelsSouth: 4, connections: ["north", "south", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 0, 4], state: "ae2:1k_crafting_storage{formed:true,powered:true}", nbt: {core: 1b, crafting_scheduling_mode: "ANY", id: "ae2:crafting_storage", inventory: [], proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [0, 0, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 1, channelsNorth: 5, channelsSouth: 6, connections: ["north", "south", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 0, 6], state: "ae2:64k_crafting_storage{formed:true,powered:true}", nbt: {core: 1b, crafting_scheduling_mode: "ANY", id: "ae2:crafting_storage", inventory: [], proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [0, 0, 7], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 7, channelsNorth: 7, connections: ["north", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 0, 9], state: "minecraft:barrel{facing:west,open:false}", nbt: {Items: [], id: "minecraft:barrel"}},
        {pos: [0, 0, 11], state: "ae2:drive{facing:west,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item1: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item2: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item3: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item4: {Count: 1b, id: "ae2:fluid_storage_cell_1k", tag: {}}, item5: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item6: {}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 663128L, p: 0}, visual: {cell0: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell1: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell2: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell3: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell4: {id: "ae2:fluid_storage_cell_1k", state: "empty"}, cell5: {id: "ae2:item_storage_cell_1k", state: "empty"}, online: 1b}}},
        {pos: [0, 0, 12], state: "ae2:io_port{facing:west,powered:true,spin:0}", nbt: {fullness_mode: "EMPTY", id: "ae2:io_port", inv: {item0: {}, item1: {}, item10: {}, item11: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, lastRedstoneState: 1, operation_mode: "EMPTY", proxy: {g: 663128L, p: 0}, redstone_controlled: "IGNORE", visual: {}}},
        {pos: [1, 0, 1], state: "ae2:1k_crafting_storage{formed:true,powered:true}", nbt: {core: 1b, crafting_scheduling_mode: "ANY", id: "ae2:crafting_storage", inventory: [], proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [1, 0, 3], state: "ae2:1k_crafting_storage{formed:true,powered:true}", nbt: {core: 1b, crafting_scheduling_mode: "ANY", id: "ae2:crafting_storage", inventory: [], proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [1, 0, 5], state: "ae2:1k_crafting_storage{formed:true,powered:true}", nbt: {core: 1b, crafting_scheduling_mode: "ANY", id: "ae2:crafting_storage", inventory: [], proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [1, 0, 7], state: "ae2:64k_crafting_storage{formed:true,powered:true}", nbt: {core: 1b, crafting_scheduling_mode: "ANY", id: "ae2:crafting_storage", inventory: [], proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [1, 0, 12], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 8, channelsWest: 8, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 0, 7], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsSouth: 8, channelsWest: 8, connections: ["south", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 0, 8], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsNorth: 8, channelsSouth: 8, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 0, 9], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsNorth: 8, channelsSouth: 8, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 0, 10], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsNorth: 8, channelsSouth: 8, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 0, 11], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsNorth: 8, channelsSouth: 8, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 0, 12], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsNorth: 8, channelsSouth: 16, channelsWest: 8, connections: ["north", "south", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 0, 13], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 16, channelsNorth: 16, connections: ["north", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 0, 13], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 16, channelsWest: 16, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 0, 2], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [4, 0, 3], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [4, 0, 5], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [4, 0, 6], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [4, 0, 9], state: "minecraft:oak_wall_sign{facing:north,waterlogged:false}", nbt: {back_text: {color: "black", has_glowing_text: 0b, messages: ['{"text":""}', '{"text":""}', '{"text":""}', '{"text":""}']}, front_text: {color: "black", has_glowing_text: 0b, messages: ['{"text":"Some Machine"}', '{"text":""}', '{"text":""}', '{"text":""}']}, id: "minecraft:sign", is_waxed: 0b}},
        {pos: [4, 0, 10], state: "minecraft:blast_furnace{facing:north,lit:false}", nbt: {BurnTime: 0s, CookTime: 0s, CookTimeTotal: 0s, Items: [], RecipesUsed: {}, id: "minecraft:blast_furnace"}},
        {pos: [4, 0, 11], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 1}, returnInv: [], sendList: [], visual: {}}},
        {pos: [4, 0, 12], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 1, channelsSouth: 2, channelsUp: 1, connections: ["up", "north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 0, 13], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 18, channelsNorth: 2, channelsWest: 16, connections: ["north", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 0, 2], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [5, 0, 3], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [5, 0, 5], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [5, 0, 6], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [5, 0, 13], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsSouth: 18, channelsWest: 18, connections: ["south", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 0, 14], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 18, channelsNorth: 18, connections: ["north", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 0, 8], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:white_smart_dense_cable", visual: {channelsSouth: 32, channelsUp: 32, connections: ["up", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 0, 9], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:white_smart_dense_cable", visual: {channelsNorth: 32, channelsSouth: 32, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 0, 10], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:white_smart_dense_cable", visual: {channelsNorth: 32, channelsSouth: 32, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 0, 11], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:white_smart_dense_cable", visual: {channelsNorth: 32, channelsSouth: 32, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 0, 12], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:white_smart_dense_cable", visual: {channelsNorth: 32, channelsSouth: 32, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 0, 13], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:white_smart_dense_cable", visual: {channelsNorth: 32, channelsSouth: 32, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 0, 14], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [7, 0, 2], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [7, 0, 3], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [7, 0, 5], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [7, 0, 6], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [7, 0, 13], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 24, channelsSouth: 24, connections: ["south", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 0, 14], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsNorth: 24, channelsWest: 24, connections: ["north", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [8, 0, 2], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [8, 0, 3], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [8, 0, 5], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [8, 0, 6], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [8, 0, 13], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 24, channelsWest: 24, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [9, 0, 13], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 24, channelsWest: 24, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 0, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 8, channelsSouth: 8, connections: ["south", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 0, 6], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsNorth: 8, channelsSouth: 8, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, east: {id: "ae2:cable_anchor", visual: {}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 0, 7], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsNorth: 8, channelsSouth: 8, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 0, 8], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsNorth: 8, channelsSouth: 8, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 0, 9], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 8, channelsNorth: 8, channelsSouth: 16, connections: ["north", "south", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 0, 10], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsNorth: 16, channelsSouth: 16, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 0, 11], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsNorth: 16, channelsSouth: 16, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 0, 12], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsEast: 8, channelsNorth: 16, channelsSouth: 24, connections: ["north", "south", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [10, 0, 13], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_dense_cable", visual: {channelsNorth: 24, channelsWest: 24, connections: ["north", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [11, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsSouth: 2, channelsUp: 1, connections: ["up", "south"], missingChannel: 0b, powered: 1b}}, east: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 663128L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [11, 0, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 2, channelsSouth: 2, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [11, 0, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 2, channelsSouth: 4, channelsUp: 1, connections: ["up", "north", "south"], missingChannel: 0b, powered: 1b}}, east: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 663128L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [11, 0, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 4, channelsSouth: 2, channelsUp: 1, channelsWest: 8, connections: ["up", "north", "south", "west"], missingChannel: 0b, powered: 1b}}, east: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 663128L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [11, 0, 6], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 2, channelsUp: 1, connections: ["up", "north"], missingChannel: 0b, powered: 1b}}, east: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 663128L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {id: "ae2:cable_anchor", visual: {}}}},
        {pos: [11, 0, 9], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 8, channelsWest: 8, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [11, 0, 12], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 8, channelsWest: 8, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [12, 0, 1], state: "minecraft:chest{facing:east,type:left,waterlogged:false}", nbt: {Items: [], id: "minecraft:chest"}},
        {pos: [12, 0, 2], state: "minecraft:chest{facing:east,type:right,waterlogged:false}", nbt: {Items: [], id: "minecraft:chest"}},
        {pos: [12, 0, 3], state: "minecraft:chest{facing:east,type:left,waterlogged:false}", nbt: {Items: [], id: "minecraft:chest"}},
        {pos: [12, 0, 4], state: "minecraft:chest{facing:east,type:right,waterlogged:false}", nbt: {Items: [], id: "minecraft:chest"}},
        {pos: [12, 0, 5], state: "minecraft:barrel{facing:east,open:false}", nbt: {Items: [], id: "minecraft:barrel"}},
        {pos: [12, 0, 6], state: "minecraft:barrel{facing:east,open:false}", nbt: {Items: [], id: "minecraft:barrel"}},
        {pos: [12, 0, 8], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item1: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item2: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item3: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item4: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item5: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item6: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item7: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item8: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item9: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}}, priority: 0, proxy: {g: 663128L, p: 0}, visual: {cell0: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell1: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell2: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell3: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell4: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell5: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell6: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell7: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell8: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell9: {id: "ae2:item_storage_cell_256k", state: "empty"}, online: 1b}}},
        {pos: [12, 0, 9], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {}, item1: {}, item2: {}, item3: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item4: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item5: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item6: {}, item7: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item8: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item9: {}}, priority: 0, proxy: {g: 663128L, p: 0}, visual: {cell3: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell4: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell5: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell7: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell8: {id: "ae2:item_storage_cell_1k", state: "empty"}, online: 1b}}},
        {pos: [12, 0, 11], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {}, item1: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item2: {}, item3: {}, item4: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item5: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item6: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item7: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item8: {}, item9: {}}, priority: 0, proxy: {g: 663128L, p: 0}, visual: {cell1: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell4: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell5: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell6: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell7: {id: "ae2:item_storage_cell_256k", state: "empty"}, online: 1b}}},
        {pos: [12, 0, 12], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item1: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item2: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item3: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item4: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item5: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item6: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item7: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item8: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item9: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}}, priority: 0, proxy: {g: 663128L, p: 0}, visual: {cell0: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell1: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell2: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell3: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell4: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell5: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell6: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell7: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell8: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell9: {id: "ae2:item_storage_cell_256k", state: "empty"}, online: 1b}}},
        {pos: [0, 1, 0], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_unit", proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [0, 1, 2], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_unit", proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [0, 1, 4], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_unit", proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [0, 1, 6], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_unit", proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [0, 1, 9], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsSouth: 2, connections: ["south"], missingChannel: 0b, powered: 1b}}, down: {fuzzy_mode: "IGNORE_ALL", gn: {g: 663128L, p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {gn: {g: 663128L, p: 0}, id: "ae2:pattern_access_terminal", show_pattern_providers: "VISIBLE", spin: 0b, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [0, 1, 10], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 2, channelsSouth: 4, channelsUp: 1, connections: ["up", "north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {filter_type: "ALL", gn: {g: 663128L, p: 0}, id: "ae2:terminal", sort_by: "NAME", sort_direction: "ASCENDING", spin: 0b, view_mode: "ALL", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [0, 1, 11], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 5, channelsNorth: 4, channelsSouth: 0, connections: ["down", "north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {filter_type: "ALL", gn: {g: 663128L, p: 0}, id: "ae2:crafting_terminal", sort_by: "NAME", sort_direction: "ASCENDING", spin: 0b, view_mode: "ALL", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [0, 1, 12], state: "ae2:cable_bus{light_level:9,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 1, channelsNorth: 0, connections: ["down", "north"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {filter_type: "ALL", gn: {g: 663128L, p: 0}, id: "ae2:pattern_encoding_terminal", mode: "CRAFTING", sort_by: "NAME", sort_direction: "ASCENDING", spin: 0b, substitute: 0b, substituteFluids: 1b, view_mode: "ALL", visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [1, 1, 1], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_unit", proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [1, 1, 3], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_unit", proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [1, 1, 5], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_unit", proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [1, 1, 7], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_unit", proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [4, 1, 2], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [4, 1, 3], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [4, 1, 5], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [4, 1, 6], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [4, 1, 10], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25616L, p: 1}, id: "ae2:green_smart_cable", visual: {channelsSouth: 4, connections: ["south"], missingChannel: 0b, powered: 1b}}, down: {fuzzy_mode: "IGNORE_ALL", gn: {g: 25616L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {fuzzy_mode: "IGNORE_ALL", gn: {g: 25616L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [4, 1, 11], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 25616L, p: 1}, id: "ae2:green_smart_cable", visual: {channelsNorth: 4, connections: ["north"], missingChannel: 0b, powered: 1b}}, down: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 25616L, p: 1}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", south: {gn: {g: 25616L, p: 1}, id: "ae2:quartz_fiber", outer: {g: 663128L, p: 1}, visual: {missingChannel: 0b, powered: 1b}}, up: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 25616L, p: 1}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [4, 1, 12], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsDown: 1, channelsNorth: 0, channelsUp: 1, connections: ["down", "up", "north"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 1, 2], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [5, 1, 3], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [5, 1, 5], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [5, 1, 6], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [6, 1, 8], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:white_smart_dense_cable", visual: {channelsDown: 32, channelsUp: 32, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 1, 2], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [7, 1, 3], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [7, 1, 5], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [7, 1, 6], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [8, 1, 2], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [8, 1, 3], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [8, 1, 5], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [8, 1, 6], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [11, 1, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 1, connections: ["down"], missingChannel: 0b, powered: 1b}}, east: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 663128L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [11, 1, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 1, channelsSouth: 0, connections: ["down", "south"], missingChannel: 0b, powered: 1b}}, east: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 663128L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [11, 1, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 1, channelsNorth: 0, channelsSouth: 0, connections: ["down", "north", "south"], missingChannel: 0b, powered: 1b}}, east: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 663128L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [11, 1, 6], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 1, channelsNorth: 0, connections: ["down", "north"], missingChannel: 0b, powered: 1b}}, east: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {g: 663128L, p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [12, 1, 1], state: "minecraft:chest{facing:east,type:left,waterlogged:false}", nbt: {Items: [], id: "minecraft:chest"}},
        {pos: [12, 1, 2], state: "minecraft:chest{facing:east,type:right,waterlogged:false}", nbt: {Items: [], id: "minecraft:chest"}},
        {pos: [12, 1, 3], state: "minecraft:chest{facing:east,type:left,waterlogged:false}", nbt: {Items: [], id: "minecraft:chest"}},
        {pos: [12, 1, 4], state: "minecraft:chest{facing:east,type:right,waterlogged:false}", nbt: {Items: [], id: "minecraft:chest"}},
        {pos: [12, 1, 5], state: "minecraft:barrel{facing:east,open:false}", nbt: {Items: [], id: "minecraft:barrel"}},
        {pos: [12, 1, 6], state: "minecraft:barrel{facing:east,open:false}", nbt: {Items: [], id: "minecraft:barrel"}},
        {pos: [12, 1, 8], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item1: {}, item2: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 663128L, p: 0}, visual: {cell0: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell2: {id: "ae2:item_storage_cell_1k", state: "empty"}, online: 1b}}},
        {pos: [12, 1, 9], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {}, item1: {}, item2: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item3: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item4: {}, item5: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item6: {}, item7: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item8: {}, item9: {}}, priority: 0, proxy: {g: 663128L, p: 0}, visual: {cell2: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell3: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell5: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell7: {id: "ae2:item_storage_cell_1k", state: "empty"}, online: 1b}}},
        {pos: [12, 1, 11], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {}, item1: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item2: {}, item3: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item4: {}, item5: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item6: {}, item7: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item8: {Count: 1b, id: "ae2:fluid_storage_cell_1k", tag: {}}, item9: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}}, priority: 0, proxy: {g: 663128L, p: 0}, visual: {cell1: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell3: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell5: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell7: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell8: {id: "ae2:fluid_storage_cell_1k", state: "empty"}, cell9: {id: "ae2:item_storage_cell_1k", state: "empty"}, online: 1b}}},
        {pos: [12, 1, 12], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:fluid_storage_cell_1k", tag: {}}, item1: {Count: 1b, id: "ae2:fluid_storage_cell_1k", tag: {}}, item2: {Count: 1b, id: "ae2:fluid_storage_cell_1k", tag: {}}, item3: {Count: 1b, id: "ae2:fluid_storage_cell_1k", tag: {}}, item4: {Count: 1b, id: "ae2:fluid_storage_cell_1k", tag: {}}, item5: {Count: 1b, id: "ae2:fluid_storage_cell_1k", tag: {}}, item6: {Count: 1b, id: "ae2:fluid_storage_cell_1k", tag: {}}, item7: {Count: 1b, id: "ae2:fluid_storage_cell_1k", tag: {}}, item8: {Count: 1b, id: "ae2:fluid_storage_cell_1k", tag: {}}, item9: {Count: 1b, id: "ae2:fluid_storage_cell_1k", tag: {}}}, priority: 0, proxy: {g: 663128L, p: 0}, visual: {cell0: {id: "ae2:fluid_storage_cell_1k", state: "empty"}, cell1: {id: "ae2:fluid_storage_cell_1k", state: "empty"}, cell2: {id: "ae2:fluid_storage_cell_1k", state: "empty"}, cell3: {id: "ae2:fluid_storage_cell_1k", state: "empty"}, cell4: {id: "ae2:fluid_storage_cell_1k", state: "empty"}, cell5: {id: "ae2:fluid_storage_cell_1k", state: "empty"}, cell6: {id: "ae2:fluid_storage_cell_1k", state: "empty"}, cell7: {id: "ae2:fluid_storage_cell_1k", state: "empty"}, cell8: {id: "ae2:fluid_storage_cell_1k", state: "empty"}, cell9: {id: "ae2:fluid_storage_cell_1k", state: "empty"}, online: 1b}}},
        {pos: [0, 2, 0], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_unit", proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [0, 2, 2], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_unit", proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [0, 2, 4], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_unit", proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [0, 2, 6], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_unit", proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [0, 2, 10], state: "ae2:wireless_access_point{facing:up,state:has_channel,waterlogged:false}", nbt: {id: "ae2:wireless_access_point", inv: {item0: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [1, 2, 1], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_unit", proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [1, 2, 3], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_unit", proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [1, 2, 5], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_unit", proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [1, 2, 7], state: "ae2:crafting_accelerator{formed:true,powered:true}", nbt: {core: 0b, id: "ae2:crafting_unit", proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [4, 2, 2], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [4, 2, 3], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [4, 2, 5], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [4, 2, 6], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [4, 2, 9], state: "minecraft:oak_wall_sign{facing:north,waterlogged:false}", nbt: {back_text: {color: "black", has_glowing_text: 0b, messages: ['{"text":""}', '{"text":""}', '{"text":""}', '{"text":""}']}, front_text: {color: "black", has_glowing_text: 0b, messages: ['{"text":"Some Machine"}', '{"text":""}', '{"text":""}', '{"text":""}']}, id: "minecraft:sign", is_waxed: 0b}},
        {pos: [4, 2, 10], state: "minecraft:blast_furnace{facing:north,lit:false}", nbt: {BurnTime: 0s, CookTime: 0s, CookTimeTotal: 0s, Items: [], RecipesUsed: {}, id: "minecraft:blast_furnace"}},
        {pos: [4, 2, 11], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 1}, returnInv: [], sendList: [], visual: {}}},
        {pos: [4, 2, 12], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsDown: 1, channelsNorth: 1, connections: ["down", "north"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 2, 2], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [5, 2, 3], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [5, 2, 5], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [5, 2, 6], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [6, 2, 8], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:white_smart_dense_cable", visual: {channelsDown: 32, channelsUp: 32, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 2, 2], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [7, 2, 3], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [7, 2, 5], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [7, 2, 6], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [8, 2, 2], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [8, 2, 3], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [8, 2, 5], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [8, 2, 6], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [12, 2, 8], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {}, item1: {}, item2: {}, item3: {}, item4: {Count: 1b, id: "ae2:fluid_storage_cell_1k", tag: {}}, item5: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item6: {Count: 1b, id: "ae2:fluid_storage_cell_1k", tag: {}}, item7: {}, item8: {Count: 1b, id: "ae2:fluid_storage_cell_1k", tag: {}}, item9: {}}, priority: 0, proxy: {g: 663128L, p: 0}, visual: {cell4: {id: "ae2:fluid_storage_cell_1k", state: "empty"}, cell5: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell6: {id: "ae2:fluid_storage_cell_1k", state: "empty"}, cell8: {id: "ae2:fluid_storage_cell_1k", state: "empty"}, online: 1b}}},
        {pos: [12, 2, 9], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {}, item1: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 663128L, p: 0}, visual: {online: 1b}}},
        {pos: [12, 2, 11], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item1: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item2: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item3: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item4: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item5: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item6: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item7: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item8: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item9: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}}, priority: 0, proxy: {g: 663128L, p: 0}, visual: {cell0: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell1: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell2: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell3: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell4: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell5: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell6: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell7: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell8: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell9: {id: "ae2:item_storage_cell_1k", state: "empty"}, online: 1b}}},
        {pos: [12, 2, 12], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {}, item1: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item2: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item3: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item4: {}, item5: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item6: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item7: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item8: {}, item9: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}}, priority: 0, proxy: {g: 663128L, p: 0}, visual: {cell1: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell2: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell3: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell5: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell6: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell7: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell9: {id: "ae2:item_storage_cell_1k", state: "empty"}, online: 1b}}},
        {pos: [4, 3, 2], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [4, 3, 3], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [4, 3, 5], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [4, 3, 6], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [5, 3, 2], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [5, 3, 3], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [5, 3, 5], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [5, 3, 6], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [6, 3, 8], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:white_smart_dense_cable", visual: {channelsDown: 32, channelsUp: 32, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 3, 2], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [7, 3, 3], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [7, 3, 5], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [7, 3, 6], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [8, 3, 2], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [8, 3, 3], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [8, 3, 5], state: "ae2:molecular_assembler{powered:true}", nbt: {id: "ae2:molecular_assembler", inv: {item0: {}, item1: {}, item10: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, proxy: {g: 663128L, p: 0}, visual: {}}},
        {pos: [8, 3, 6], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 663128L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [12, 3, 8], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {}, item1: {}, item2: {}, item3: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item4: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item5: {}, item6: {}, item7: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item8: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item9: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}}, priority: 0, proxy: {g: 663128L, p: 0}, visual: {cell3: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell4: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell7: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell8: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell9: {id: "ae2:item_storage_cell_1k", state: "empty"}, online: 1b}}},
        {pos: [12, 3, 9], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item1: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item2: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item3: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item4: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item5: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item6: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item7: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item8: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item9: {}}, priority: 0, proxy: {g: 663128L, p: 0}, visual: {cell0: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell1: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell2: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell3: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell4: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell5: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell6: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell7: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell8: {id: "ae2:item_storage_cell_1k", state: "empty"}, online: 1b}}},
        {pos: [12, 3, 11], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item1: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item2: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item3: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item4: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item5: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item6: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item7: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item8: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item9: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}}, priority: 0, proxy: {g: 663128L, p: 0}, visual: {cell0: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell1: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell2: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell3: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell4: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell5: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell6: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell7: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell8: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell9: {id: "ae2:item_storage_cell_1k", state: "empty"}, online: 1b}}},
        {pos: [12, 3, 12], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item1: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item2: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item3: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item4: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item5: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item6: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item7: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item8: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}, item9: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {}}}, priority: 0, proxy: {g: 663128L, p: 0}, visual: {cell0: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell1: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell2: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell3: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell4: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell5: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell6: {id: "ae2:item_storage_cell_256k", state: "empty"}, cell7: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell8: {id: "ae2:item_storage_cell_1k", state: "empty"}, cell9: {id: "ae2:item_storage_cell_1k", state: "empty"}, online: 1b}}},
        {pos: [5, 4, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:white_smart_cable", visual: {channelsDown: 8, channelsSouth: 8, connections: ["down", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 4, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:white_smart_dense_cable", visual: {channelsEast: 16, channelsNorth: 8, channelsSouth: 8, connections: ["north", "south", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [5, 4, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:white_smart_cable", visual: {channelsDown: 8, channelsNorth: 8, connections: ["down", "north"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 4, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:white_smart_dense_cable", visual: {channelsEast: 16, channelsUp: 32, channelsWest: 16, connections: ["up", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 4, 8], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:white_smart_dense_cable", visual: {channelsDown: 32, channelsUp: 32, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 4, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:white_smart_cable", visual: {channelsDown: 8, channelsSouth: 8, connections: ["down", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 4, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:white_smart_dense_cable", visual: {channelsNorth: 8, channelsSouth: 8, channelsWest: 16, connections: ["north", "south", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [7, 4, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:white_smart_cable", visual: {channelsDown: 8, channelsNorth: 8, connections: ["down", "north"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 5, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:white_smart_dense_cable", visual: {channelsDown: 32, channelsSouth: 32, connections: ["down", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 5, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:white_smart_dense_cable", visual: {channelsNorth: 32, channelsSouth: 32, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 5, 6], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:white_smart_dense_cable", visual: {channelsNorth: 32, channelsSouth: 32, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 5, 7], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:white_smart_dense_cable", visual: {channelsNorth: 32, channelsSouth: 32, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [6, 5, 8], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 663128L, p: 0}, id: "ae2:white_smart_dense_cable", visual: {channelsDown: 32, channelsNorth: 32, connections: ["down", "north"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}}
    ],
    entities: [],
    palette: [
        "ae2:1k_crafting_storage{formed:true,powered:true}",
        "ae2:cable_bus{light_level:0,waterlogged:false}",
        "ae2:64k_crafting_storage{formed:true,powered:true}",
        "minecraft:barrel{facing:west,open:false}",
        "ae2:drive{facing:west,spin:0}",
        "ae2:io_port{facing:west,powered:true,spin:0}",
        "ae2:molecular_assembler{powered:true}",
        "ae2:pattern_provider{push_direction:all}",
        "minecraft:oak_wall_sign{facing:north,waterlogged:false}",
        "minecraft:blast_furnace{facing:north,lit:false}",
        "ae2:controller{state:online,type:block}",
        "minecraft:chest{facing:east,type:left,waterlogged:false}",
        "minecraft:chest{facing:east,type:right,waterlogged:false}",
        "minecraft:barrel{facing:east,open:false}",
        "ae2:drive{facing:east,spin:0}",
        "ae2:crafting_accelerator{formed:true,powered:true}",
        "ae2:cable_bus{light_level:9,waterlogged:false}",
        "ae2:wireless_access_point{facing:up,state:has_channel,waterlogged:false}"
    ]
}