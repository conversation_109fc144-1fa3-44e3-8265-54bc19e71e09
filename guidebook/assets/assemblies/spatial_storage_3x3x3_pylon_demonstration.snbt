{
    DataVersion: 3460,
    size: [5, 5, 5],
    data: [
        {pos: [0, 0, 0], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 250093L, p: 0}, visual: {axis: "Z", axisPosition: "START", online: 1b, powered: 1b}}},
        {pos: [0, 0, 1], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 250093L, p: 0}, visual: {axis: "Z", axisPosition: "MIDDLE", online: 1b, powered: 1b}}},
        {pos: [0, 0, 2], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 250093L, p: 0}, visual: {axis: "Z", axisPosition: "MIDDLE", online: 1b, powered: 1b}}},
        {pos: [0, 0, 3], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 250093L, p: 0}, visual: {axis: "Z", axisPosition: "MIDDLE", online: 1b, powered: 1b}}},
        {pos: [0, 0, 4], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 250093L, p: 0}, visual: {axis: "Z", axisPosition: "END", online: 1b, powered: 1b}}},
        {pos: [1, 0, 0], state: "ae2:spatial_io_port{facing:north,powered:true,spin:0}", nbt: {id: "ae2:spatial_io_port", inv: {item0: {}, item1: {}}, lastRedstoneState: 1, proxy: {g: 250093L, p: 0}, visual: {}}},
        {pos: [1, 0, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 239734L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 1, channelsWest: 1, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 0, 0], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 239734L, p: 0}, visual: {}}},
        {pos: [2, 0, 2], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 239734L, p: 0}, visual: {axis: "Z", axisPosition: "START", online: 1b, powered: 1b}}},
        {pos: [2, 0, 3], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 239734L, p: 0}, visual: {axis: "Z", axisPosition: "END", online: 1b, powered: 1b}}},
        {pos: [3, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 239734L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 6, channelsWest: 6, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 0, 0], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 239734L, p: 0}, visual: {axis: "Z", axisPosition: "START", online: 1b, powered: 1b}}},
        {pos: [4, 0, 1], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 239734L, p: 0}, visual: {axis: "Z", axisPosition: "MIDDLE", online: 1b, powered: 1b}}},
        {pos: [4, 0, 2], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 239734L, p: 0}, visual: {axis: "Z", axisPosition: "MIDDLE", online: 1b, powered: 1b}}},
        {pos: [4, 0, 3], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 239734L, p: 0}, visual: {axis: "Z", axisPosition: "END", online: 1b, powered: 1b}}},
        {pos: [4, 0, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 239734L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 5, channelsUp: 5, connections: ["up", "north"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 1, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 239734L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 2, channelsUp: 2, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 1, 0], state: "minecraft:oak_button{face:floor,facing:south,powered:false}"},
        {pos: [4, 1, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 239734L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 5, channelsUp: 5, connections: ["down", "up"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 2, 0], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 239734L, p: 0}, visual: {axis: "Y", axisPosition: "START", online: 1b, powered: 1b}}},
        {pos: [0, 2, 4], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 239734L, p: 0}, visual: {axis: "X", axisPosition: "START", online: 1b, powered: 1b}}},
        {pos: [1, 2, 4], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 239734L, p: 0}, visual: {axis: "X", axisPosition: "END", online: 1b, powered: 1b}}},
        {pos: [2, 2, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 239734L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsEast: 3, channelsUp: 2, channelsWest: 1, connections: ["up", "west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 2, 4], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 239734L, p: 0}, visual: {axis: "X", axisPosition: "START", online: 1b, powered: 1b}}},
        {pos: [4, 2, 4], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 239734L, p: 0}, visual: {axis: "X", axisPosition: "END", online: 1b, powered: 1b}}},
        {pos: [0, 3, 0], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 239734L, p: 0}, visual: {axis: "Y", axisPosition: "MIDDLE", online: 1b, powered: 1b}}},
        {pos: [0, 3, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 239734L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 1, channelsSouth: 1, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 3, 2], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 239734L, p: 0}, visual: {axis: "Z", axisPosition: "START", online: 1b, powered: 1b}}},
        {pos: [0, 3, 3], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 239734L, p: 0}, visual: {axis: "Z", axisPosition: "END", online: 1b, powered: 1b}}},
        {pos: [2, 3, 4], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 239734L, p: 0}, visual: {axis: "Y", axisPosition: "START", online: 1b, powered: 1b}}},
        {pos: [4, 3, 2], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 239734L, p: 0}, visual: {axis: "Z", axisPosition: "START", online: 1b, powered: 1b}}},
        {pos: [4, 3, 3], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 239734L, p: 0}, visual: {axis: "Z", axisPosition: "END", online: 1b, powered: 1b}}},
        {pos: [4, 3, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 239734L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsDown: 1, channelsNorth: 1, connections: ["down", "north"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 4, 0], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 239734L, p: 0}, visual: {axis: "Y", axisPosition: "END", online: 1b, powered: 1b}}},
        {pos: [2, 4, 0], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 239734L, p: 0}, visual: {axis: "Z", axisPosition: "START", online: 1b, powered: 1b}}},
        {pos: [2, 4, 1], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 239734L, p: 0}, visual: {axis: "Z", axisPosition: "END", online: 1b, powered: 1b}}},
        {pos: [2, 4, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 239734L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 1, channelsSouth: 1, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 4, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 239734L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 1, channelsSouth: 1, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 4, 4], state: "ae2:spatial_pylon{powered_on:true}", nbt: {id: "ae2:spatial_pylon", proxy: {g: 239734L, p: 0}, visual: {axis: "Y", axisPosition: "END", online: 1b, powered: 1b}}}
    ],
    entities: [],
    palette: [
        "minecraft:oak_button{face:floor,facing:south,powered:false}",
        "ae2:spatial_pylon{powered_on:true}",
        "ae2:spatial_io_port{facing:north,powered:true,spin:0}",
        "ae2:cable_bus{light_level:0,waterlogged:false}",
        "ae2:controller{state:online,type:block}"
    ]
}