{
    DataVersion: 3337,
    size: [2, 1, 2],
    data: [
        {pos: [0, 0, 0], state: "ae2:pattern_provider{push_direction:east}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 343109L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [0, 0, 1], state: "ae2:pattern_provider{push_direction:all}", nbt: {blocking_mode: "NO", id: "ae2:pattern_provider", lock_crafting_mode: "NONE", pattern_access_terminal: "YES", priority: 0, proxy: {g: 343109L, p: 0}, returnInv: [], sendList: [], visual: {}}},
        {pos: [1, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 343109L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsSouth: 2, connections: ["south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 343109L, p: 0}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 2, channelsWest: 2, connections: ["north", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}}
    ],
    entities: [],
    palette: [
        "ae2:pattern_provider{push_direction:east}",
        "ae2:pattern_provider{push_direction:all}",
        "ae2:cable_bus{light_level:0,waterlogged:false}"
    ]
}