{
    DataVersion: 3337,
    size: [4, 4, 6],
    data: [
        {pos: [0, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 28300L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsEast: 8, channelsSouth: 8, connections: ["south", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 0, 2], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 28300L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 8, channelsSouth: 8, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 0, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 28300L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 8, channelsSouth: 8, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 0, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 28300L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 8, channelsSouth: 8, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 0, 5], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 28300L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsEast: 8, channelsNorth: 8, connections: ["north", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 28300L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsEast: 8, channelsWest: 8, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 0, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 28300L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsEast: 8, channelsSouth: 8, connections: ["south", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {id: "ae2:cable_anchor", visual: {}}}},
        {pos: [1, 0, 4], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 28300L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 8, channelsSouth: 8, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {id: "ae2:cable_anchor", visual: {}}}},
        {pos: [1, 0, 5], state: "ae2:controller{state:online,type:block}", nbt: {id: "ae2:controller", internalCurrentPower: 0.0d, proxy: {g: 28300L, k: -1L, p: 2}, visual: {}}},
        {pos: [2, 0, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 28300L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsEast: 8, channelsWest: 8, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 0, 3], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 28300L, k: -1L, p: 2}, id: "ae2:blue_smart_cable", visual: {channelsEast: 8, channelsWest: 8, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 0, 0], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item1: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 28300L, k: -1L, p: 2}, visual: {cell0: {id: "ae2:item_storage_cell_256k", state: "empty"}, online: 1b}}},
        {pos: [3, 0, 1], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item1: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 28300L, k: -1L, p: 2}, visual: {cell0: {id: "ae2:item_storage_cell_256k", state: "empty"}, online: 1b}}},
        {pos: [3, 0, 3], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item1: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 28300L, k: -1L, p: 2}, visual: {cell0: {id: "ae2:item_storage_cell_256k", state: "empty"}, online: 1b}}},
        {pos: [3, 0, 4], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item1: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 28300L, k: -1L, p: 2}, visual: {cell0: {id: "ae2:item_storage_cell_256k", state: "empty"}, online: 1b}}},
        {pos: [3, 1, 0], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item1: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 28300L, k: -1L, p: 2}, visual: {cell0: {id: "ae2:item_storage_cell_256k", state: "empty"}, online: 1b}}},
        {pos: [3, 1, 1], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item1: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 28300L, k: -1L, p: 2}, visual: {cell0: {id: "ae2:item_storage_cell_256k", state: "empty"}, online: 1b}}},
        {pos: [3, 1, 3], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item1: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 28300L, k: -1L, p: 2}, visual: {cell0: {id: "ae2:item_storage_cell_256k", state: "empty"}, online: 1b}}},
        {pos: [3, 1, 4], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item1: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 28300L, k: -1L, p: 2}, visual: {cell0: {id: "ae2:item_storage_cell_256k", state: "empty"}, online: 1b}}},
        {pos: [3, 2, 0], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item1: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 28300L, k: -1L, p: 2}, visual: {cell0: {id: "ae2:item_storage_cell_256k", state: "empty"}, online: 1b}}},
        {pos: [3, 2, 1], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item1: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 28300L, k: -1L, p: 2}, visual: {cell0: {id: "ae2:item_storage_cell_256k", state: "empty"}, online: 1b}}},
        {pos: [3, 2, 3], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item1: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 28300L, k: -1L, p: 2}, visual: {cell0: {id: "ae2:item_storage_cell_256k", state: "empty"}, online: 1b}}},
        {pos: [3, 2, 4], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item1: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 28300L, k: -1L, p: 2}, visual: {cell0: {id: "ae2:item_storage_cell_256k", state: "empty"}, online: 1b}}},
        {pos: [3, 3, 0], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item1: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 28300L, k: -1L, p: 2}, visual: {cell0: {id: "ae2:item_storage_cell_256k", state: "empty"}, online: 1b}}},
        {pos: [3, 3, 1], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item1: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 28300L, k: -1L, p: 2}, visual: {cell0: {id: "ae2:item_storage_cell_256k", state: "empty"}, online: 1b}}},
        {pos: [3, 3, 3], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item1: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 28300L, k: -1L, p: 2}, visual: {cell0: {id: "ae2:item_storage_cell_256k", state: "empty"}, online: 1b}}},
        {pos: [3, 3, 4], state: "ae2:drive{facing:east,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_256k", tag: {}}, item1: {}, item2: {}, item3: {}, item4: {}, item5: {}, item6: {}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 28300L, k: -1L, p: 2}, visual: {cell0: {id: "ae2:item_storage_cell_256k", state: "empty"}, online: 1b}}}
    ],
    entities: [],
    palette: [
        "ae2:cable_bus{light_level:0,waterlogged:false}",
        "ae2:controller{state:online,type:block}",
        "ae2:drive{facing:east,spin:0}"
    ]
}