{
    DataVersion: 3465,
    size: [5, 2, 2],
    data: [
        {pos: [0, 0, 0], state: "minecraft:oak_wall_sign{facing:north,waterlogged:false}", nbt: {back_text: {color: "black", has_glowing_text: 0b, messages: ['{"text":""}', '{"text":""}', '{"text":""}', '{"text":""}']}, front_text: {color: "black", has_glowing_text: 0b, messages: ['{"text":"CHARGED"}', '{"text":"CERTUS"}', '{"text":""}', '{"text":""}']}, id: "minecraft:sign", is_waxed: 0b}},
        {pos: [0, 0, 1], state: "minecraft:barrel{facing:north,open:false}", nbt: {Items: [], id: "minecraft:barrel"}},
        {pos: [2, 0, 1], state: "ae2:charger{facing:north,spin:0}", nbt: {id: "ae2:charger", internalCurrentPower: 1600.0d, inv: {item0: {}}, proxy: {p: 0}, visual: {}}},
        {pos: [4, 0, 0], state: "minecraft:oak_wall_sign{facing:north,waterlogged:false}", nbt: {back_text: {color: "black", has_glowing_text: 0b, messages: ['{"text":""}', '{"text":""}', '{"text":""}', '{"text":""}']}, front_text: {color: "black", has_glowing_text: 0b, messages: ['{"text":"CERTUS"}', '{"text":""}', '{"text":""}', '{"text":""}']}, id: "minecraft:sign", is_waxed: 0b}},
        {pos: [4, 0, 1], state: "minecraft:barrel{facing:north,open:false}", nbt: {Items: [], id: "minecraft:barrel"}},
        {pos: [0, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:pink_smart_cable", visual: {channelsEast: 3, connections: ["east"], missingChannel: 0b, powered: 1b}}, down: {config: [{"#": 0L, "#c": "ae2:i", id: "ae2:charged_certus_quartz_crystal"}], craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {gn: {p: 0}, id: "ae2:quartz_fiber", outer: {p: 0}, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [1, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:pink_smart_cable", visual: {channelsEast: 3, channelsWest: 3, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [2, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:pink_smart_cable", visual: {channelsEast: 3, channelsWest: 3, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, down: {access: "READ_WRITE", filter_on_extract: "YES", fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:storage_bus", priority: 0, storage_filter: "EXTRACTABLE_ONLY", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [3, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:pink_smart_cable", visual: {channelsEast: 3, channelsWest: 3, connections: ["west", "east"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [4, 1, 1], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {p: 0}, id: "ae2:pink_smart_cable", visual: {channelsWest: 3, connections: ["west"], missingChannel: 0b, powered: 1b}}, down: {fuzzy_mode: "IGNORE_ALL", gn: {p: 0}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}}
    ],
    entities: [],
    palette: [
        "minecraft:oak_wall_sign{facing:north,waterlogged:false}",
        "minecraft:barrel{facing:north,open:false}",
        "ae2:charger{facing:north,spin:0}",
        "ae2:cable_bus{light_level:0,waterlogged:false}"
    ]
}