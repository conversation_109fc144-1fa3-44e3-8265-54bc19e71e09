{
    DataVersion: 3337,
    size: [3, 1, 16],
    data: [
        {pos: [0, 0, 13], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83588L, k: -1L, p: 1}, id: "ae2:green_smart_cable", visual: {channelsSouth: 2, connections: ["south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83588L, k: -1L, p: 1}, id: "ae2:formation_plane", place_block: "YES", priority: 0, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [0, 0, 14], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83588L, k: -1L, p: 1}, id: "ae2:green_smart_cable", visual: {channelsNorth: 2, channelsSouth: 2, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [0, 0, 15], state: "ae2:interface", nbt: {fuzzy_mode: "IGNORE_ALL", id: "ae2:interface", priority: 0, proxy: {g: 83588L, k: -1L, p: 1}, visual: {}}},
        {pos: [1, 0, 14], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83576L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsSouth: 0, connections: ["south"], missingChannel: 0b, powered: 1b}}, east: {gn: {g: 83576L, k: -1L, p: 1}, id: "ae2:quartz_fiber", outer: {g: 83584L, k: -1L, p: 1}, visual: {missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}}},
        {pos: [1, 0, 15], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83576L, k: -1L, p: 1}, id: "ae2:blue_smart_cable", visual: {channelsNorth: 0, channelsSouth: 0, connections: ["north", "south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", visual: {}, west: {gn: {g: 83576L, k: -1L, p: 1}, id: "ae2:quartz_fiber", outer: {g: 83588L, k: -1L, p: 1}, visual: {missingChannel: 0b, powered: 1b}}}},
        {pos: [2, 0, 13], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83584L, k: -1L, p: 1}, id: "ae2:green_smart_cable", visual: {channelsSouth: 2, connections: ["south"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", north: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83584L, k: -1L, p: 1}, id: "ae2:formation_plane", place_block: "YES", priority: 0, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [2, 0, 14], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 83584L, k: -1L, p: 1}, id: "ae2:green_smart_cable", visual: {channelsNorth: 2, channelsWest: 2, connections: ["north", "west"], missingChannel: 0b, powered: 1b}}, hasRedstone: 1, id: "ae2:cable_bus", south: {fuzzy_mode: "IGNORE_ALL", gn: {g: 83584L, k: -1L, p: 1}, id: "ae2:import_bus", redstone_controlled: "IGNORE", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}},
        {pos: [2, 0, 15], state: "minecraft:barrel{facing:up,open:false}", nbt: {Items: [], id: "minecraft:barrel"}}
    ],
    entities: [],
    palette: [
        "ae2:cable_bus{light_level:0,waterlogged:false}",
        "ae2:interface",
        "minecraft:barrel{facing:up,open:false}"
    ]
}