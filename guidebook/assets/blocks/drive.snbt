{
    DataVersion: 3337,
    size: [1, 1, 1],
    data: [
        {pos: [0, 0, 0], state: "ae2:drive{facing:north,spin:0}", nbt: {id: "ae2:drive", inv: {item0: {Count: 1b, id: "ae2:item_storage_cell_64k", tag: {}}, item1: {Count: 1b, id: "ae2:fluid_storage_cell_4k", tag: {amts: [L; 81000L], ic: 81000L, keys: [{"#c": "ae2:f", id: "minecraft:water"}]}}, item2: {Count: 1b, id: "ae2:item_storage_cell_4k", tag: {amts: [L; 1L], ic: 1L, keys: [{"#c": "ae2:i", id: "minecraft:redstone"}]}}, item3: {}, item4: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {amts: [L; 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L, 1L], ic: 63L, keys: [{"#c": "ae2:i", id: "minecraft:oak_sapling"}, {"#c": "ae2:i", id: "minecraft:redstone_ore"}, {"#c": "ae2:i", id: "minecraft:mud"}, {"#c": "ae2:i", id: "minecraft:podzol"}, {"#c": "ae2:i", id: "minecraft:raw_iron_block"}, {"#c": "ae2:i", id: "minecraft:acacia_planks"}, {"#c": "ae2:i", id: "minecraft:birch_sapling"}, {"#c": "ae2:i", id: "minecraft:ancient_debris"}, {"#c": "ae2:i", id: "minecraft:lapis_ore"}, {"#c": "ae2:i", id: "minecraft:polished_andesite"}, {"#c": "ae2:i", id: "minecraft:dark_oak_sapling"}, {"#c": "ae2:i", id: "minecraft:gold_ore"}, {"#c": "ae2:i", id: "minecraft:deepslate_copper_ore"}, {"#c": "ae2:i", id: "minecraft:nether_quartz_ore"}, {"#c": "ae2:i", id: "minecraft:copper_ore"}, {"#c": "ae2:i", id: "minecraft:andesite"}, {"#c": "ae2:i", id: "minecraft:oak_planks"}, {"#c": "ae2:i", id: "minecraft:sand"}, {"#c": "ae2:i", id: "minecraft:diamond_ore"}, {"#c": "ae2:i", id: "minecraft:dirt"}, {"#c": "ae2:i", id: "minecraft:jungle_sapling"}, {"#c": "ae2:i", id: "minecraft:spruce_sapling"}, {"#c": "ae2:i", id: "minecraft:cobblestone"}, {"#c": "ae2:i", id: "minecraft:red_sand"}, {"#c": "ae2:i", id: "minecraft:warped_planks"}, {"#c": "ae2:i", id: "minecraft:jungle_planks"}, {"#c": "ae2:i", id: "minecraft:coarse_dirt"}, {"#c": "ae2:i", id: "minecraft:warped_nylium"}, {"#c": "ae2:i", id: "minecraft:coal_ore"}, {"#c": "ae2:i", id: "minecraft:deepslate_diamond_ore"}, {"#c": "ae2:i", id: "minecraft:mangrove_planks"}, {"#c": "ae2:i", id: "minecraft:deepslate_emerald_ore"}, {"#c": "ae2:i", id: "minecraft:tuff"}, {"#c": "ae2:i", id: "minecraft:acacia_sapling"}, {"#c": "ae2:i", id: "minecraft:gravel"}, {"#c": "ae2:i", id: "minecraft:rooted_dirt"}, {"#c": "ae2:i", id: "minecraft:deepslate"}, {"#c": "ae2:i", id: "minecraft:stone"}, {"#c": "ae2:i", id: "minecraft:emerald_ore"}, {"#c": "ae2:i", id: "minecraft:cobbled_deepslate"}, {"#c": "ae2:i", id: "minecraft:deepslate_iron_ore"}, {"#c": "ae2:i", id: "minecraft:grass_block"}, {"#c": "ae2:i", id: "minecraft:calcite"}, {"#c": "ae2:i", id: "minecraft:bedrock"}, {"#c": "ae2:i", id: "minecraft:granite"}, {"#c": "ae2:i", id: "minecraft:deepslate_gold_ore"}, {"#c": "ae2:i", id: "minecraft:deepslate_lapis_ore"}, {"#c": "ae2:i", id: "minecraft:deepslate_coal_ore"}, {"#c": "ae2:i", id: "minecraft:crimson_planks"}, {"#c": "ae2:i", id: "minecraft:diorite"}, {"#c": "ae2:i", id: "minecraft:polished_diorite"}, {"#c": "ae2:i", id: "minecraft:nether_gold_ore"}, {"#c": "ae2:i", id: "minecraft:polished_granite"}, {"#c": "ae2:i", id: "minecraft:birch_planks"}, {"#c": "ae2:i", id: "minecraft:polished_deepslate"}, {"#c": "ae2:i", id: "minecraft:iron_ore"}, {"#c": "ae2:i", id: "minecraft:spruce_planks"}, {"#c": "ae2:i", id: "minecraft:mangrove_propagule"}, {"#c": "ae2:i", id: "minecraft:deepslate_redstone_ore"}, {"#c": "ae2:i", id: "minecraft:crimson_nylium"}, {"#c": "ae2:i", id: "minecraft:dripstone_block"}, {"#c": "ae2:i", id: "minecraft:dark_oak_planks"}, {"#c": "ae2:i", id: "minecraft:coal_block"}]}}, item5: {}, item6: {Count: 1b, id: "ae2:item_storage_cell_1k", tag: {amts: [L; 8128L], ic: 8128L, keys: [{"#c": "ae2:i", id: "minecraft:cobblestone"}]}}, item7: {}, item8: {}, item9: {}}, priority: 0, proxy: {g: 136048L, k: -1L, p: 0}, visual: {cell0: {id: "ae2:item_storage_cell_64k", state: "empty"}, cell1: {id: "ae2:fluid_storage_cell_4k", state: "not_empty"}, cell2: {id: "ae2:item_storage_cell_4k", state: "not_empty"}, cell4: {id: "ae2:item_storage_cell_1k", state: "types_full"}, cell6: {id: "ae2:item_storage_cell_1k", state: "full"}, online: 1b}}}
    ],
    entities: [],
    palette: [
        "ae2:drive{facing:north,spin:0}"
    ]
}