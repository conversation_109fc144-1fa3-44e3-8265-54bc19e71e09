{
    DataVersion: 3337,
    size: [1, 1, 1],
    data: [
        {pos: [0, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27333L, k: -1L, p: 0}, id: "ae2:fluix_glass_cable", visual: {channelsDown: 1, connections: ["down"], missingChannel: 0b, powered: 1b}}, hasRedstone: 1, id: "ae2:cable_bus", up: {craft_only: "NO", fuzzy_mode: "IGNORE_ALL", gn: {g: 27333L, k: -1L, p: 0}, id: "ae2:export_bus", nextSlot: 0, redstone_controlled: "IGNORE", scheduling_mode: "DEFAULT", visual: {missingChannel: 0b, powered: 1b}}, visual: {}}}
    ],
    entities: [],
    palette: [
        "ae2:cable_bus{light_level:0,waterlogged:false}"
    ]
}