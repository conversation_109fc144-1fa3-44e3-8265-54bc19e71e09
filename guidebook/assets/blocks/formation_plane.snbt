{
    DataVersion: 3337,
    size: [1, 1, 1],
    data: [
        {pos: [0, 0, 0], state: "ae2:cable_bus{light_level:0,waterlogged:false}", nbt: {cable: {gn: {g: 27333L, k: -1L, p: 0}, id: "ae2:fluix_glass_cable", visual: {channelsDown: 1, connections: ["down"], missingChannel: 0b, powered: 1b}}, hasRedstone: 2, id: "ae2:cable_bus", up: {fuzzy_mode: "IGNORE_ALL", gn: {g: 27333L, k: -1L, p: 0}, id: "ae2:formation_plane", place_block: "YES", priority: 0, visual: {missingChannel: 0b, powered: 1b}}, visual: {}}}
    ],
    entities: [],
    palette: [
        "ae2:cable_bus{light_level:0,waterlogged:false}"
    ]
}