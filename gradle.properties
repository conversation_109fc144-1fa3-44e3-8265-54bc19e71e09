version=15.4.6

artifact_group=appeng
artifact_basename=appliedenergistics2-forge

#########################################################
# Minecraft Versions                                    #
#########################################################
minecraft_release=1.20
minecraft_version=1.20.1
minecraft_version_range=[1.20.1,1.20.2)
forge_version=47.1.47
forge_version_range=[47.1.3,)

#########################################################
# Provided APIs                                         #
#########################################################
guideme_version=20.1.7
guideme_version_range=[20.1.7,20.2.0)
jei_minecraft_version=1.20.1
jei_version=*********
jei_version_range=[15.0.0,16.0.0)
top_minecraft_release=1.20
top_version=9.0.0
# please learn how to use semver...
top_version_range=[1.20.0,)
jade_version_range=[11.0.0,)
cloth_config_version=11.1.136
architectury_version=9.2.14
rei_version=12.1.785
wthit_version=8.1.0
jade_file_id=4573193

# Pick which item list mod gets picked at runtime in dev
# Available options: jei, rei, none
runtime_itemlist_mod=rei

# Set to wthit, jade, top or none to pick which tooltip mod gets picked at runtime
# for the dev environment.
runtime_tooltip_mod=none

#########################################################
# Third party dependencies
#########################################################
snakeyaml_version=1.33
directory_watcher_version=0.17.1
junit_version=5.10.0-M1
flatbuffers_version=23.5.26

#########################################################
# Gradle                                                #
#########################################################
# Temp fix for Spotless / Remove Unused Imports:
# https://github.com/diffplug/spotless/issues/834
org.gradle.jvmargs=-Xmx2G \
  --add-exports jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED \
  --add-exports jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED \
  --add-exports jdk.compiler/com.sun.tools.javac.parser=ALL-UNNAMED \
  --add-exports jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED \
  --add-exports jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED
