pluginManagement {
    repositories {
        maven {
            name = 'NeoForged'
            url = 'https://maven.neoforged.net/releases'
        }
        maven {
            name = "Sponge"
            url = 'https://repo.spongepowered.org/maven'
        }
        gradlePluginPortal()
        mavenCentral()
    }
    plugins {
        id 'net.neoforged.moddev.legacyforge' version '2.0.78'
        id 'com.diffplug.spotless' version '6.25.0'
        id 'com.gradleup.shadow' version '8.3.5'
        id 'de.undercouch.download' version '5.4.0'
    }
}

include 'libs:markdown'
