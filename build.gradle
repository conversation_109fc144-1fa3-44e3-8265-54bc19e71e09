/*
 * This file is part of Applied Energistics 2.
 * Copyright (c) 2013 - 2015, AlgorithmX2, All rights reserved.
 *
 * Applied Energistics 2 is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Applied Energistics 2 is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with Applied Energistics 2.  If not, see <http://www.gnu.org/licenses/lgpl>.
 */

plugins {
    id "net.neoforged.moddev.legacyforge"
    id "maven-publish"
    id "com.diffplug.spotless"
}

sourceSets {
    main {
        resources {
            srcDir 'src/generated/resources'
        }
    }
    test {
    }
    buildtools
}

configurations {
    shaded {
        transitive = false
    }
    buildtoolsImplementation.extendsFrom(compileClasspath)
    // Dependencies only used for the guide export, but not shipped
    guideExportOnly
    configurations.compileClasspath.extendsFrom(guideExportOnly)
    configurations.runtimeClasspath.extendsFrom(guideExportOnly)
}

import com.diffplug.gradle.spotless.JsonExtension

// All jar files from this folder will be added automatically as runtime mod dependencies
def extraModsDir = "extra-mods-${minecraft_version}"
file(extraModsDir).mkdir()

dependencies {
    modImplementation("org.appliedenergistics:guideme:${guideme_version}")

    // Used for the guide export
    guideExportOnly("org.bytedeco:ffmpeg-platform:6.0-1.5.9")

    // compile against provided APIs
    modCompileOnly "mezz.jei:jei-${jei_minecraft_version}-common-api:${jei_version}"
    modCompileOnly "mezz.jei:jei-${jei_minecraft_version}-forge-api:${jei_version}"

    // Always depend on the REI API to compile
    if (project.runtime_itemlist_mod == "jei") {
        modRuntimeOnly "mezz.jei:jei-${jei_minecraft_version}-forge:${jei_version}"

        compileOnly "me.shedaniel.cloth:basic-math:0.6.1"
        modCompileOnly "dev.architectury:architectury-forge:${project.architectury_version}"
        modCompileOnly "me.shedaniel:RoughlyEnoughItems-forge:${project.rei_version}"
    } else if (project.runtime_itemlist_mod == "rei") {
        implementation "me.shedaniel.cloth:basic-math:0.6.1"
        modImplementation "me.shedaniel.cloth:cloth-config-forge:${project.cloth_config_version}"
        modImplementation "dev.architectury:architectury-forge:${project.architectury_version}"
        modImplementation "me.shedaniel:RoughlyEnoughItems-forge:${project.rei_version}"
    } else {
        modCompileOnly "me.shedaniel.cloth:cloth-config-forge:${project.cloth_config_version}"
        modCompileOnly "dev.architectury:architectury-forge:${project.architectury_version}"
        modCompileOnly "me.shedaniel:RoughlyEnoughItems-forge:${project.rei_version}"
    }

    // Locally sourced extra mods for runtime (i.e. testing)
    for (extraModJar in fileTree(dir: extraModsDir, include: '*.jar')) {
        def basename = extraModJar.name.substring(0, extraModJar.name.length() - ".jar".length())
        def versionSep = basename.lastIndexOf('-')
        assert versionSep != -1
        def artifactId = basename.substring(0, versionSep)
        def version = basename.substring(versionSep + 1)
        modRuntimeOnly "extra-mods:$artifactId:$version"
    }

    if (project.runtime_tooltip_mod == "wthit") {
        modRuntimeOnly "mcp.mobius.waila:wthit:forge-${project.wthit_version}"
        modRuntimeOnly "lol.bai:badpackets:forge-0.1.2"
    } else {
        modCompileOnly "mcp.mobius.waila:wthit-api:forge-${project.wthit_version}"
    }

    if (project.runtime_tooltip_mod == "jade") {
        modImplementation "curse.maven:jade-324717:${project.jade_file_id}"
    } else {
        modCompileOnly "curse.maven:jade-324717:${project.jade_file_id}"
    }

    def topDependency = "curse.maven:the-one-probe-245211:4159743"
    if (project.runtime_tooltip_mod == "top") {
        modImplementation topDependency
    } else {
        compileOnly topDependency
    }

    // unit test dependencies
    testImplementation(platform("org.junit:junit-bom:${project.junit_version}"))
    testImplementation("org.junit.jupiter:junit-jupiter-api")
    testImplementation("org.junit.jupiter:junit-jupiter-params")
    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine")
    testImplementation("org.junit.platform:junit-platform-launcher")
    testImplementation("org.assertj:assertj-core:3.19.0")
    testImplementation("com.google.guava:guava-testlib:21.0")
    testImplementation("org.mockito:mockito-junit-jupiter:5.3.1")

    compileOnly 'org.apache.commons:commons-configuration2:2.9.0'
    compileOnly 'org.jetbrains:annotations:24.1.0'
    testCompileOnly 'org.jetbrains:annotations:24.1.0'

    // Annotation Processors
    annotationProcessor 'org.spongepowered:mixin:0.8.5:processor'
}
archivesBaseName = artifact_basename

allprojects {
    group = artifact_group

    java {
        toolchain {
            languageVersion = JavaLanguageVersion.of(17)
        }
    }

    repositories {
        mavenLocal()
        mavenCentral()
        maven {
            url "https://maven.shedaniel.me/"
            content {
                includeGroup "me.shedaniel"
                includeGroup "me.shedaniel.cloth"
                includeGroup "dev.architectury"
            }
        }
        maven {
            url "https://maven2.bai.lol"
            content {
                includeGroup "mcp.mobius.waila"
                includeGroup "lol.bai"
            }
        }
        maven {
            url "https://maven.parchmentmc.net/"
            content {
                includeGroup "org.parchmentmc.data"
            }
        }
        // For the "No Indium?" mod
        maven {
            url = 'https://maven.cafeteria.dev/releases/'
            content {
                includeGroup "me.luligabi"
            }
        }

        maven {
            name 'modmaven'
            url "https://modmaven.dev/"
            content {
                includeGroup "mezz.jei"
            }
        }

        maven {
            name 'cursemaven'
            url "https://www.cursemaven.com"
            content {
                includeGroup "curse.maven"
            }
        }

        maven {
            url "https://maven.blamejared.com"
            content {
                includeGroup "vazkii.patchouli"
            }
        }

        flatDir {
            name "extra-mods"
            dir file(extraModsDir)
        }
        maven { // for TOP
            url "https://maven.k-4u.nl/"
            content {
                includeGroup "mcjty"
            }
        }
        maven { url = 'https://repo.spongepowered.org/maven' }
    }

    // ensure everything uses UTF-8 and not some random codepage chosen by gradle
    compileJava.options.encoding = 'UTF-8'
    tasks.withType(JavaCompile) {
        options.encoding = 'UTF-8'
        options.deprecation = false
        options.compilerArgs << "-Xmaxerrs" << "9999"
    }
}

///////////////////
// Version Number

ext.pr = System.getenv('PR_NUMBER') ?: ""
if (ext.pr) {
    version = version + "+pr." + ext.pr
}

ext.branch = System.getenv('BRANCH') ?: ""
if (ext.branch) {
    version = version + "+branch." + ext.branch
}

ext.tag = System.getenv('TAG') ?: ""
if (ext.tag) {
    if (!ext.tag.startsWith("forge/v")) {
        throw new GradleException("Tags for the forge version should start with forge/: ${ext.tag}")
    }

    version = ext.tag.substring("forge/v".length())
    // Validate that the rest is a semver version
    if (version ==~ /^(0|[1-9]\d*)\.(0|[1-9]\d*)\.(0|[1-9]\d*)(?:-((?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\.(?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?$/) {
        if (System.getenv("GITHUB_ENV")) {
            def envFile = new File(System.getenv("GITHUB_ENV"))
            envFile.append("VERSION=${version}")
        }
    } else {
        throw new GradleException("Invalid semver: $version")
    }
}

ext.isAlpha = project.version.contains("alpha")
ext.isBeta = project.version.contains("beta")

test {
    useJUnitPlatform()

    // Might not need this anymore...
    systemProperty "guideme.ae2.guide.sources", file("guidebook").absolutePath
}

dependencies {
    buildtoolsImplementation 'de.siegmar:fastcsv:2.1.0'
    buildtoolsImplementation 'com.google.code.gson:gson:2.8.9'
}

/**
 * Configures properties common to all run configurations
 */
Map<String, String> commonSystemProperties = [
        'forge.logging.console.level': 'debug',
        'appeng.tests'               : 'true',
        'appeng.version'             : project.version
]

legacyForge {
    version = "${minecraft_version}-${forge_version}"

    mods {
        ae2 {
            sourceSet sourceSets.main
        }
    }

    runs {
        configureEach {
            gameDirectory = project.file('run')
            systemProperties.putAll(commonSystemProperties)
            // property "mixin.debug.export", "true"

            additionalRuntimeClasspathConfiguration.extendsFrom configurations.shaded
            additionalRuntimeClasspathConfiguration.extendsFrom configurations.guideExportOnly

            jvmArguments.addAll(
                    "--add-opens", "java.base/sun.security.util=ALL-UNNAMED",
                    "--add-opens", "java.base/java.util.jar=ALL-UNNAMED"
            )

            systemProperty "guideme.ae2.guide.sources", file("guidebook").absolutePath
        }
        client {
            client()
        }
        gametestWorld {
            client()
            programArguments.addAll(
                    "--username", "AE2Dev", "--quickPlaySingleplayer", "GametestWorld"
            )
        }
        guide {
            client()

            systemProperty('guideme.showOnStartup', 'ae2:guide')
        }
        server {
            server()
        }
        data {
            data()
            programArguments.addAll(
                    '--mod', 'ae2',
                    '--all',
                    '--output', file('src/generated/resources/').absolutePath,
                    '--existing', file('src/main/resources').absolutePath
            )
        }
        guideexport {
            client()
            systemProperty('appeng.runGuideExportAndExit', 'true')
            systemProperty('appeng.guideExportFolder', file('build/guide').absolutePath)
        }
        // Use to run the tests
        gametest {
            type = "gameTestServer"
            gameDirectory = project.file("build/gametest")
        }
    }

    addModdingDependenciesTo(sourceSets.test)
}

//////////////
// Artifacts
Map<String, String> expansionVariables = [
        "project_version"  : project.version,
        'minecraft_version': project.minecraft_version_range,
        'forge_version'    : project.forge_version_range,
        'guideme_version'  : project.guideme_version_range,
        'jei_version'      : project.jei_version_range,
        'top_version'      : project.top_version_range,
        'jade_version'     : project.jade_version_range
]

processResources {
    exclude '.cache'
    // Ensure the resources get re-evaluate when the version changes
    for (var entry : expansionVariables.entrySet()) {
        inputs.property(entry.key, entry.value)
    }

    filesMatching("META-INF/mods.toml") {
        expand expansionVariables
        filter { line ->
            line.replace('version="0.0.0"', "version=\"${expansionVariables['project_version']}\"")
        }
    }
}

///////////
// Mixins
mixin {
    var refmap = add sourceSets.main, "ae2.mixins.refmap.json"
    jar.from refmap
    config "ae2.mixins.json"
}

jar {
    from('guidebook') {
        into 'assets/ae2/ae2guide'
    }
    manifest {
        attributes([
                "MixinConfigs"          : "ae2.mixins.json"
        ])
    }
}

def publicApiIncludePatterns = {
    exclude "**/*Internal.*"
    exclude "**/*Internal\$*.*"
    include "appeng/api/**"
}

javadoc {
    source = sourceSets.main.allJava
    classpath = sourceSets.main.compileClasspath + sourceSets.main.output

    options.addStringOption('Xdoclint:none', '-quiet')
    options.encoding = 'UTF-8'
    options.charSet = 'UTF-8'
}
javadoc publicApiIncludePatterns

task javadocJar(type: Jar, dependsOn: javadoc, group: "build") {
    archiveClassifier = "javadoc"
    from javadoc.destinationDir
}

task sourcesJar(type: Jar) {
    archiveClassifier = "sources"
    from sourceSets.main.allJava
}

task apiJar(type: Jar, group: "build") {
    archiveClassifier = "api"
    // api jar ist just a development aid and serves as both a binary and source jar simultaneously
    from sourceSets.main.output
    from sourceSets.main.allJava
    manifest {
        attributes("Fabric-Loom-Remap": true)
    }
}
apiJar publicApiIncludePatterns

artifacts {
    archives javadocJar
    archives sourcesJar
    archives apiJar
}

//////////////////
// Maven publish
publishing {
    publications {
        maven(MavenPublication) {
            groupId = project.group
            artifactId = project.archivesBaseName
            version = project.version

            artifact jar
            artifact sourcesJar
            artifact javadocJar
            artifact apiJar
        }
    }
    repositories {
        maven {
            name = "Local"
            url = file("build/repo").toURI()
        }
    }
}

/////////////
// Spotless
spotless {

    java {
        target 'src/*/java/appeng/**/*.java'

        endWithNewline()
        indentWithSpaces()
        removeUnusedImports()
        toggleOffOn()
        eclipse().configFile 'codeformat/codeformat.xml'
        importOrderFile 'codeformat/ae2.importorder'

        // courtesy of diffplug/spotless#240
        // https://github.com/diffplug/spotless/issues/240#issuecomment-385206606
        custom 'noWildcardImports', {
            if (it.contains('*;\n')) {
                throw new Error('No wildcard imports allowed')
            }

            it
        }
        bumpThisNumberIfACustomStepChanges(1)
    }

   json {
        target 'src/*/resources/**/*.json'
        targetExclude 'src/generated/resources/**'
        var biomeConfig = it.new JsonExtension.BiomeJson(null)
        try {
            biomeConfig.downloadDir(new File(rootDir, ".gradle/biome").absolutePath)
        } catch (Exception ignored) {
        }
        addStep(biomeConfig.createStep())
        indentWithSpaces(2)
        endWithNewline()
    }
}

////////////////
// Crowdin
tasks.register('uploadToCrowdin', JavaExec) {
    classpath = sourceSets.buildtools.runtimeClasspath
    mainClass = 'Crowdin'
    args 'upload_source'
    workingDir "."
}
tasks.register('uploadTranslations', JavaExec) {
    classpath = sourceSets.buildtools.runtimeClasspath
    mainClass = 'Crowdin'
    args 'upload_translations'
    workingDir "."
}
tasks.register('downloadFromCrowdin', JavaExec) {
    classpath = sourceSets.buildtools.runtimeClasspath
    mainClass = 'Crowdin'
    args 'update_translations'
    workingDir "."
}

// See https://github.com/AppliedEnergistics/Applied-Energistics-2/issues/5259
// Gradle module metadata contains mapped dependencies, making our artifacts unconsumable
tasks.withType(GenerateModuleMetadata) {
    enabled = false
}

check.dependsOn tasks.register('validateResources', JavaExec) {
    group = "verification"
    classpath = sourceSets.buildtools.runtimeClasspath
    mainClass = 'ValidateResourceIds'
    workingDir "."
    args "guidebook"
    javaLauncher.set(javaToolchains.launcherFor(java.toolchain))
}

